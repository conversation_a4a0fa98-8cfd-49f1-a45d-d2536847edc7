build:
    chmod +x build.sh
    ./build.sh

serve:
    npm run start

clean:
    rm -rf .next
    rm -rf dist
    rm -rf .npm-cache
    rm -rf node_modules/.cache

docker-build:
    docker build -t phone-number-hash-web .

docker-run:
    docker run -p 80:80 phone-number-hash-web

docker-compose:
    docker compose up -d
	
pm2-start:
    npm run build
    pm2 start ecosystem.config.js
    
server:
    npm run dev 
