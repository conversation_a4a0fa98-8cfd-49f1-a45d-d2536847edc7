"use client";

import type React from "react";

import { useState } from "react";
import { Toaster, toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Upload, CheckCircle, Phone, Mail, MapPin } from "lucide-react";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import SuccessModal from "@/components/success-modal";
import { registerClient } from "@/services/api";
import { processImage } from "@/lib/processFile";

type imagePreview = {
  name: string;
  type?: string;
  url: string;
};

export default function RegistrationPage() {
  var attachment = {
    async: false,
    content_md5: "",
    content_size: 0,
    content_type: "",
    duration: 0,
    data: "",
  };

  const [formData, setFormData] = useState({
    business_name: "",
    business_pin: "",
    company_email: "",
    company_address: "",
    company_phone_number: "",
    director_name: "",
    director_address: "",
    director_id_front: attachment,
    director_id_back: attachment,
    director_phone_number: "",
    director_email: "",
    director_pin: "",
    form_filled_by: "",
    trader_invoicing_system: "",
  });
  const [commitmentConsent, setCommitmentConsent] = useState({
    commitment1: false,
    commitment2: false,
  });

  const [directorIdFrontFile, setDirectorIdFrontFile] = useState<File | null>(
    null
  );

  const [directorIdBackFile, setDirectorIdBackFile] = useState<File | null>(
    null
  );

  const [filePreview, setFilePreview] = useState<{
    director_id_front_preview: null | imagePreview;
    director_id_back_preview: null | imagePreview;
  }>({
    director_id_front_preview: null,
    director_id_back_preview: null,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successModalOpen, setSuccessModalOpen] = useState(false);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileUpload = (field: string, file: File | null) => {
    if (!file) return;
    // check size limit
    if (file.size > 11 * 1024 * 1024) {
      toast.error("Selected file exceeds the maximum size of 10MB");
      return;
    }

    processImage(file)
      .then((data) => {
        setFilePreview((prev) => ({
          ...prev,
          [field]: {
            name: data.name,
            type: data.content_type,
            url: URL.createObjectURL(file),
          },
        }));

        setFormData((prev) => ({
          ...prev,
          [field]: data,
        }));
      })
      .catch((err) => {
        console.error("processing image", err);
        toast.error("Failed to process the image. Please try again.");
      });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    var attachment = {
      async: false,
      content_md5: "",
      content_size: 0,
      content_type: "",
      duration: 0,
      data: "",
    };

    setIsSubmitting(true);

    registerClient(formData)
      .then((res) => {
        setSuccessModalOpen(true);
        setFormData({
          business_name: "",
          business_pin: "",
          company_email: "",
          company_address: "",
          company_phone_number: "",
          director_name: "",
          director_address: "",
          director_phone_number: "",
          director_email: "",
          director_pin: "",
          form_filled_by: "",
          director_id_front: attachment,
          director_id_back: attachment,
          trader_invoicing_system: "",
        });

        setCommitmentConsent({
          commitment1: false,
          commitment2: false,
        });

        setFilePreview({
          director_id_front_preview: null,
          director_id_back_preview: null,
        });
      })
      .catch((error: any) => {
        toast.error("Registration Failed. Please try again.");
        console.error("Registration error:", error);
      })
      .finally(() => setIsSubmitting(false));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-orange-500 to-orange-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Badge className="bg-white/20 text-white px-4 py-2 mb-4">
              eTIMS Registration
            </Badge>
            <h1 className="text-4xl lg:text-5xl font-bold mb-4">
              Register Your Business for eTIMS
            </h1>
            <p className="text-xl opacity-90 max-w-3xl mx-auto">
              Complete your eTIMS client registration quickly and easily. Our
              team will process your application and get you KRA compliant.
            </p>
          </div>
        </div>
      </section>

      {/* Registration Form */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Form */}
              <div className="lg:col-span-2">
                <Card className="border-0 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-t-lg">
                    <CardTitle className="text-2xl text-gray-900">
                      eTIMS Client Registration Form
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      Please fill in all required information accurately
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-8">
                    <form onSubmit={handleSubmit} className="space-y-8">
                      {/* Company Information */}
                      <div className="space-y-6">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="bg-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                            1
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900">
                            Company/Business Information
                          </h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label
                              htmlFor="companyName"
                              className="text-sm font-medium"
                            >
                              Company/Business Name *
                            </Label>
                            <Input
                              id="companyName"
                              value={formData.business_name}
                              onChange={(e) =>
                                handleInputChange(
                                  "business_name",
                                  e.target.value
                                )
                              }
                              placeholder="Enter company name"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label
                              htmlFor="pin"
                              className="text-sm font-medium"
                            >
                              PIN *
                            </Label>
                            <Input
                              id="pin"
                              value={formData.business_pin}
                              onChange={(e) =>
                                handleInputChange(
                                  "business_pin",
                                  e.target.value
                                )
                              }
                              placeholder="Enter PIN number"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                        </div>

                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label
                              htmlFor="companyEmail"
                              className="text-sm font-medium"
                            >
                              Company Email *
                            </Label>
                            <Input
                              id="companyEmail"
                              type="email"
                              value={formData.company_email}
                              onChange={(e) =>
                                handleInputChange(
                                  "company_email",
                                  e.target.value
                                )
                              }
                              placeholder="<EMAIL>"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label
                              htmlFor="companyPhone"
                              className="text-sm font-medium"
                            >
                              Company Phone *
                            </Label>
                            <Input
                              id="companyPhone"
                              value={formData.company_phone_number}
                              onChange={(e) =>
                                handleInputChange(
                                  "company_phone_number",
                                  e.target.value
                                )
                              }
                              placeholder="+*********** 000"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label
                            htmlFor="companyAddress"
                            className="text-sm font-medium"
                          >
                            Company Address *
                          </Label>
                          <Textarea
                            id="companyAddress"
                            value={formData.company_address}
                            onChange={(e) =>
                              handleInputChange(
                                "company_address",
                                e.target.value
                              )
                            }
                            placeholder="Enter complete company address"
                            className="border-gray-300 focus:border-orange-500 min-h-[100px]"
                            required
                          />
                        </div>
                      </div>

                      <Separator />

                      {/* Director Information */}
                      <div className="space-y-6">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="bg-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                            2
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900">
                            Director's Information
                          </h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label
                              htmlFor="directorName"
                              className="text-sm font-medium"
                            >
                              Director's Name *
                            </Label>
                            <Input
                              id="directorName"
                              value={formData.director_name}
                              onChange={(e) =>
                                handleInputChange(
                                  "director_name",
                                  e.target.value
                                )
                              }
                              placeholder="Enter director's full name"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label
                              htmlFor="directorPhone"
                              className="text-sm font-medium"
                            >
                              Director's Phone *
                            </Label>
                            <Input
                              id="directorPhone"
                              value={formData.director_phone_number}
                              onChange={(e) =>
                                handleInputChange(
                                  "director_phone_number",
                                  e.target.value
                                )
                              }
                              placeholder="+*********** 000"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                        </div>

                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label
                              htmlFor="directorEmail"
                              className="text-sm font-medium"
                            >
                              Director's Email *
                            </Label>
                            <Input
                              id="directorEmail"
                              type="email"
                              value={formData.director_email}
                              onChange={(e) =>
                                handleInputChange(
                                  "director_email",
                                  e.target.value
                                )
                              }
                              placeholder="<EMAIL>"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label
                              htmlFor="personalPin"
                              className="text-sm font-medium"
                            >
                              Personal PIN *
                            </Label>
                            <Input
                              id="personalPin"
                              value={formData.director_pin}
                              onChange={(e) =>
                                handleInputChange(
                                  "director_pin",
                                  e.target.value
                                )
                              }
                              placeholder="Enter personal PIN"
                              className="border-gray-300 focus:border-orange-500"
                              required
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label
                            htmlFor="directorAddress"
                            className="text-sm font-medium"
                          >
                            Director's Address *
                          </Label>
                          <Textarea
                            id="directorAddress"
                            value={formData.director_address}
                            onChange={(e) =>
                              handleInputChange(
                                "director_address",
                                e.target.value
                              )
                            }
                            placeholder="Enter director's complete address"
                            className="border-gray-300 focus:border-orange-500 min-h-[100px]"
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label
                            htmlFor="traderInvoicingSystem"
                            className="text-sm font-medium"
                          >
                            Trader Invoicing System
                          </Label>
                          <Input
                            id="traderInvoicingSystem"
                            value={formData.trader_invoicing_system}
                            onChange={(e) =>
                              handleInputChange(
                                "trader_invoicing_system",
                                e.target.value
                              )
                            }
                            placeholder="Enter your trader invoicing system"
                            className="border-gray-300 focus:border-orange-500"
                          />
                        </div>
                      </div>

                      <Separator />

                      {/* Document Upload */}
                      <div className="space-y-6">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="bg-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                            3
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900">
                            Document Upload
                          </h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              Upload Director's ID (Front) *
                            </Label>
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-orange-500 transition-colors">
                              <input
                                type="file"
                                // todo: support .pdf too
                                accept=".png,.jpg,.jpeg"
                                multiple={false}
                                onChange={(e) =>
                                  handleFileUpload(
                                    "director_id_front",
                                    e.target.files?.[0] || null
                                  )
                                }
                                className="hidden"
                                id="frontIdFile"
                              />

                              {filePreview.director_id_front_preview?.url ? (
                                <img
                                  className="w-full h-[132px] object-cover cursor-pointer"
                                  src={
                                    filePreview.director_id_front_preview.url
                                  }
                                  alt={
                                    filePreview.director_id_front_preview.name
                                  }
                                  onClick={() =>
                                    document
                                      .getElementById("frontIdFile")
                                      ?.click()
                                  }
                                />
                              ) : (
                                <>
                                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                  <p className="text-sm text-gray-600 mb-2">
                                    Click below button to upload
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    PNG, JPG, JPEG up to 5MB
                                  </p>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    className="mt-2 bg-transparent"
                                    onClick={() =>
                                      document
                                        .getElementById("frontIdFile")
                                        ?.click()
                                    }
                                  >
                                    Choose File
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              Upload Director's ID (Back) *
                            </Label>
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-orange-500 transition-colors">
                              {filePreview.director_id_back_preview?.url ? (
                                <img
                                  className="w-full h-[132px] object-cover cursor-pointer"
                                  src={filePreview.director_id_back_preview.url}
                                  alt={
                                    filePreview.director_id_back_preview.name
                                  }
                                  onClick={() =>
                                    document
                                      .getElementById("backIdFile")
                                      ?.click()
                                  }
                                />
                              ) : (
                                <>
                                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                  <p className="text-sm text-gray-600 mb-2">
                                    Click below button to upload
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    PNG, JPG, JPEG up to 5MB
                                  </p>

                                  <Button
                                    type="button"
                                    variant="outline"
                                    className="mt-2 bg-transparent"
                                    onClick={() =>
                                      document
                                        .getElementById("backIdFile")
                                        ?.click()
                                    }
                                  >
                                    Choose File
                                  </Button>
                                </>
                              )}
                              <input
                                type="file"
                                accept=".png,.jpg,.jpeg"
                                multiple={false}
                                onChange={(e) =>
                                  handleFileUpload(
                                    "director_id_back",
                                    e.target.files?.[0] || null
                                  )
                                }
                                className="hidden"
                                id="backIdFile"
                              />
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label
                            htmlFor="formFilledBy"
                            className="text-sm font-medium"
                          >
                            Form Filled By
                          </Label>
                          <Input
                            id="formFilledBy"
                            value={formData.form_filled_by}
                            onChange={(e) =>
                              handleInputChange(
                                "form_filled_by",
                                e.target.value
                              )
                            }
                            placeholder="Name of person filling the form"
                            className="border-gray-300 focus:border-orange-500"
                          />
                        </div>
                      </div>

                      <Separator />

                      {/* Commitments */}
                      <div className="space-y-6">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="bg-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                            4
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900">
                            Commitment of the Taxpayer Receiving eTIMS Software
                          </h3>
                        </div>

                        <div className="space-y-4">
                          <div className="flex items-start space-x-3 p-4 bg-orange-50 rounded-lg">
                            <Checkbox
                              id="commitment1"
                              checked={commitmentConsent.commitment1}
                              onCheckedChange={(checked) =>
                                setCommitmentConsent((prev) => ({
                                  ...prev,
                                  commitment1: checked as boolean,
                                }))
                              }
                              className="mt-1"
                            />
                            <div className="space-y-1">
                              <Label
                                htmlFor="commitment1"
                                className="text-sm font-medium cursor-pointer"
                              >
                                I will not proceed with any ready to receive the
                                eTIMS software and I shall use it properly.
                              </Label>
                            </div>
                          </div>

                          <div className="flex items-start space-x-3 p-4 bg-orange-50 rounded-lg">
                            <Checkbox
                              id="commitment2"
                              checked={commitmentConsent.commitment2}
                              onCheckedChange={(checked) =>
                                setCommitmentConsent((prev) => ({
                                  ...prev,
                                  commitment2: checked as boolean,
                                }))
                              }
                              className="mt-1"
                            />
                            <div className="space-y-1">
                              <Label
                                htmlFor="commitment2"
                                className="text-sm font-medium cursor-pointer"
                              >
                                I understand that this formatting/resetting the
                                computing device cannot be formatted without KRA
                                written authorization. Otherwise, this action
                                will be considered a violation of provisions of
                                the Tax Procedures Act 2015, and VAT Act 2013
                                and similar charges as provided in the same laws
                                will be applicable.
                              </Label>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Submit Button */}
                      <div className="pt-6">
                        <Button
                          type="submit"
                          size="lg"
                          className="w-full bg-orange-500 hover:bg-orange-600 text-white py-4 text-lg font-semibold"
                          disabled={
                            !commitmentConsent.commitment1 ||
                            !commitmentConsent.commitment2 ||
                            isSubmitting
                          }
                        >
                          {isSubmitting
                            ? "Submitting..."
                            : "Submit Registration"}
                        </Button>
                        <p className="text-sm text-gray-600 text-center mt-3">
                          By submitting this form, you agree to our terms and
                          conditions
                        </p>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Requirements */}
                <Card className="border-0 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-lg">
                    <CardTitle className="text-lg">
                      Required Documents
                    </CardTitle>
                    <CardDescription>
                      Make sure you have these ready
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6 space-y-3">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Company PIN Certificate</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Director's National ID</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Business Registration</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Valid Email Address</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Phone Number</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Process Steps */}
                <Card className="border-0 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 rounded-t-lg">
                    <CardTitle className="text-lg">
                      Registration Process
                    </CardTitle>
                    <CardDescription>
                      What happens after you submit
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">
                        1
                      </div>
                      <div>
                        <p className="font-medium text-sm">Form Review</p>
                        <p className="text-xs text-gray-600">
                          We review your application within 24 hours
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">
                        2
                      </div>
                      <div>
                        <p className="font-medium text-sm">KRA Submission</p>
                        <p className="text-xs text-gray-600">
                          We submit your details to KRA for approval
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">
                        3
                      </div>
                      <div>
                        <p className="font-medium text-sm">Device Setup</p>
                        <p className="text-xs text-gray-600">
                          We configure and deliver your eTIMS device
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">
                        4
                      </div>
                      <div>
                        <p className="font-medium text-sm">Go Live</p>
                        <p className="text-xs text-gray-600">
                          Your business is now eTIMS compliant!
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Contact Information */}
                <Card className="border-0 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-slate-50 to-slate-100 rounded-t-lg">
                    <CardTitle className="text-lg">Need Help?</CardTitle>
                    <CardDescription>
                      Contact our support team for assistance
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6 space-y-4">
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium">Call Us</p>
                        <p className="text-sm text-gray-600">
                          +*********** 000
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium">Email Us</p>
                        <p className="text-sm text-gray-600">
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-5 w-5 text-orange-500 mt-1" />
                      <div>
                        <p className="font-medium">Visit Us</p>
                        <p className="text-sm text-gray-600">
                          Nairobi Business District
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />

      <SuccessModal
        open={successModalOpen}
        onClose={() => setSuccessModalOpen(false)}
      />
      <Toaster />
    </div>
  );
}
