import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/context/auth-context";

export const metadata: Metadata = {
  title: "Streamline Your eTIMS Compliance with Tactical Business Support Ltd",
  description: `Kenya's leading provider of Electronic Tax Invoice Management System (eTIMS) solutions. Get KRA compliant, boost efficiency, and grow your business with our cutting-edge technology.`,
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <AuthProvider>{children}</AuthProvider>
      </body>
    </html>
  );
}
