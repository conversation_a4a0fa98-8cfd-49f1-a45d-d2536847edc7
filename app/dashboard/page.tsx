"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CreditCard } from "lucide-react";
import { Users } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/context/auth-context";
import { analyticsService, healthService } from "@/services/api";
import { sharedFunctions } from "@/services/shared_functions";

export default function DashboardPage() {
  const { token } = useAuth();
  const [healthStatus, setHealthStatus] = useState<{
    message: string;
    status: string;
    loading: boolean;
    time: Date;
  }>({
    message: "Operational",
    status: "Ok",
    loading: false,
    time: new Date(),
  });

  const [accountBalance, setAccountBalance] = useState({
    amount: 750.0,
    loading: false,
  });

  const [accountNumber, setAccountNumber] = useState("");

  const [allCustomersCount, setAllCustomersCount] = useState(0);
  const [clientsRegisteredTodayCount, setClientsRegisteredTodayCount] =
    useState(0);
  const [clientsRegisteredThisWeekCount, setClientsRegisteredThisWeekCount] =
    useState(0);
  const [clientsRegisteredThisMonthCount, setClientsRegisteredThisMonthCount] =
    useState(0);
  const [clientsRegisteredThisYearCount, setClientsRegisteredThisYearCount] =
    useState(0);

  useEffect(() => {
    fetchDashboardAnalytics();
    fetchAccountBalance();
  }, [token]);

  const fetchDashboardAnalytics = () => {
    console.log("fetching dashboard analytics...");

    analyticsService
      .getDashboardAnalytics(token)
      .then((data) => {
        if (data && data.all_customers_count) {
          setAccountNumber(data.account_number);

          setAllCustomersCount(data.all_customers_count);

          setClientsRegisteredTodayCount(data.clients_registered_today_count);
          setClientsRegisteredThisWeekCount(
            data.clients_registered_this_week_count
          );
          setClientsRegisteredThisMonthCount(
            data.clients_registered_this_month_count
          );
          setClientsRegisteredThisYearCount(
            data.clients_registered_this_year_count
          );
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
      });
  };

  const fetchAccountBalance = () => {
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    analyticsService
      .getDashboardAnalytics(token)
      .then((data) => {
        if (data && data.user_credits) {
          setAccountBalance({
            amount: data.user_credits,
            loading: false,
          });

          setAccountNumber(data.account_number);
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
        setAccountBalance((prev) => ({ ...prev, loading: false }));
      });
  };

  const handleHealthCheck = () => {
    setHealthStatus((prev) => ({ ...prev, loading: true }));
    healthService
      .checkHealth()
      .then((data) => {
        setHealthStatus((prev) => ({
          ...prev,
          message: data.message,
          status: data.status,
          loading: false,
          time: new Date(),
        }));
      })
      .catch((error) => {
        toast.error("Failed to perform check!");
        setHealthStatus((prev) => ({ ...prev, loading: false }));
      });
  };

  return (
    <div className="flex flex-col gap-6">
      <p className="text-muted-foreground">
        Welcome back! Here's an overview of your account
      </p>

      {/* <DashboardStats /> */}

      {/* Income Analytics Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              All Customers Count
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(allCustomersCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              All registered customes count
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Clients Registered Today
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(clientsRegisteredTodayCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Clients Registered Today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Clients Registered This Week
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(clientsRegisteredThisWeekCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total clients registered this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Clients Registered This Month
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(clientsRegisteredThisMonthCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total clients registered this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <Button asChild variant="outline">
              <Link href="/dashboard/clients">View All Clients</Link>
            </Button>
            {/* <Button asChild variant="outline">
              <Link href="/dashboard/clients/new">Add New Client</Link>
            </Button> */}

            {/* dashboard/topup */}
            <Button asChild variant="outline">
              <Link href="/dashboard/topup">Billing & Topup</Link>
            </Button>

            <Button asChild variant="outline">
              <Link href="/dashboard/settings">Settings</Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API Status</CardTitle>
              <div
                className={cn(
                  "h-3 w-3 rounded-full",
                  healthStatus.status === "Ok" ? "bg-orange-500" : "bg-red-500"
                )}
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{healthStatus.message}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Last check: {format(healthStatus.time, "dd MMM yyyy")} at{" "}
                {format(healthStatus.time, "hh:mm a")}
              </p>
              <Button
                size="sm"
                className="mt-4 bg-orange-600 hover:bg-orange-700"
                onClick={handleHealthCheck}
                disabled={healthStatus.loading}
              >
                {healthStatus.loading ? "Checking..." : "Check Again"}
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Account Number
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{accountNumber}</div>
              {/* Last top-up: 100 credits on May 15, 2023 */}
              <p className="text-xs text-muted-foreground mt-1">
                For credit top ups. Paybill 4044085.
              </p>
              <Button
                size="sm"
                className="mt-4 bg-orange-600 hover:bg-orange-700"
              >
                <Link href="/dashboard/topup">Top Up Credits</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* <Tabs defaultValue="decode">
        <TabsList>
          <TabsTrigger value="decode">Decode Hash</TabsTrigger>
          <TabsTrigger value="generate">Generate Hash</TabsTrigger>
        </TabsList>
        <TabsContent value="decode" className="space-y-4"> 
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <SingleHashDecoder onDecodeSuccess={handleDecodedTransaction} />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="generate" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <SingleHashGenerator
                onGenerateSuccess={handleDecodedTransaction}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs> */}
    </div>
  );
}
