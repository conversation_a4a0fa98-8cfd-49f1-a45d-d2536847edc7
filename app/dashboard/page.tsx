"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CreditCard } from "lucide-react";
import {
  DollarSign,
  TrendingUp,
  Building,
  Hash,
  Calendar,
  Wallet,
  TrendingDown,
  BarChart3,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Legend,
} from "recharts";
import Link from "next/link";
import { useAuth } from "@/context/auth-context";
import { ExpenseAnalytics } from "@/types";
import { userIncomeService } from "@/services/user_income_service";
import { userExpenseService } from "@/services/user_expense_service";
import { analyticsService, healthService } from "@/services/api";
import { sharedFunctions } from "@/services/shared_functions";

export default function DashboardPage() {
  const { token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [healthStatus, setHealthStatus] = useState<{
    message: string;
    status: string;
    loading: boolean;
    time: Date;
  }>({
    message: "Operational",
    status: "Ok",
    loading: false,
    time: new Date(),
  });

  const [accountBalance, setAccountBalance] = useState({
    amount: 750.0,
    loading: false,
  });

  const [accountNumber, setAccountNumber] = useState("");

  const [averageMonthlyIncome, setAverageMonthlyIncome] = useState({
    amount: 0.0,
    loading: false,
  });

  const [highestPayingOrganization, setHighestPayingOrganization] = useState({
    organization_name: "",
    total_amount: 0.0,
  });

  const [totalAlltimeIncome, setTotalAlltimeIncome] = useState(0.0);
  const [totalTransactionsCount, setTotalTransactionsCount] = useState(0);
  const [highestPayingTaxCategory, setHighestPayingTaxCategory] = useState({
    tax_category_name: "",
    total_amount: 0.0,
  });
  const [currentMonthTotalIncome, setCurrentMonthTotalIncome] = useState(0.0);

  // Expense Analytics State
  const [expenseAnalytics, setExpenseAnalytics] =
    useState<ExpenseAnalytics | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [totalAlltimeExpenses, setTotalAlltimeExpenses] = useState(0.0);
  const [totalExpensesTransactionsCount, setTotalExpensesTransactionsCount] =
    useState(0);
  const [topExpenseCategory, setTopExpenseCategory] = useState({
    expense_category_name: "",
    total_amount: 0.0,
  });
  const [currentMonthTotalExpenses, setCurrentMonthTotalExpenses] =
    useState(0.0);

  // Chart data state
  const [monthlyIncomeData, setMonthlyIncomeData] = useState<any[]>([]);
  const [monthlyExpenseData, setMonthlyExpenseData] = useState<any[]>([]);
  const [combinedChartData, setCombinedChartData] = useState<any[]>([]);

  // Function to combine income and expense data
  const combinedData = () => {
    if (monthlyIncomeData.length === 0 && monthlyExpenseData.length === 0) {
      setCombinedChartData([]);
      return;
    }

    // Create a map to combine data by month
    const dataMap = new Map();

    // Add income data
    monthlyIncomeData.forEach((item) => {
      dataMap.set(item.sortKey, {
        monthName: item.monthName,
        month: item.month,
        sortKey: item.sortKey,
        income: item.total,
        expense: 0,
      });
    });

    // Add expense data
    monthlyExpenseData.forEach((item) => {
      if (dataMap.has(item.sortKey)) {
        dataMap.get(item.sortKey).expense = item.total;
      } else {
        dataMap.set(item.sortKey, {
          monthName: item.monthName,
          month: item.month,
          sortKey: item.sortKey,
          income: 0,
          expense: item.total,
        });
      }
    });

    // Convert map to array and sort
    const combined = Array.from(dataMap.values()).sort(
      (a, b) => a.sortKey - b.sortKey
    );
    setCombinedChartData(combined);
  };

  useEffect(() => {
    combinedData();
  }, [monthlyIncomeData, monthlyExpenseData]);

  useEffect(() => {
    fetchAccountBalance();

    console.log("fetching income analytics...");
    userIncomeService
      .getIncomeDashboardAnalytics(token)
      .then((data) => {
        if (data && data.income_analytics) {
          setAverageMonthlyIncome({
            amount: data.income_analytics.average_monthly_income,
            loading: false,
          });

          setHighestPayingOrganization({
            organization_name:
              data.income_analytics.highest_paying_organization
                .organization_name,
            total_amount:
              data.income_analytics.highest_paying_organization.total_amount,
          });

          setTotalAlltimeIncome(data.income_analytics.total_alltime_income);

          setTotalTransactionsCount(
            data.income_analytics.total_transactions_count
          );

          setHighestPayingTaxCategory({
            tax_category_name:
              data.income_analytics.highest_paying_tax_category
                .tax_category_name,
            total_amount:
              data.income_analytics.highest_paying_tax_category.total_amount,
          });

          setCurrentMonthTotalIncome(
            data.income_analytics.current_month_total_income
          );

          // Process monthly income data for the chart
          if (data.income_analytics.income_totals_by_month) {
            const chartData = data.income_analytics.income_totals_by_month
              .map((item: any) => ({
                month: `${item.month}/${item.year}`,
                total: item.total,
                year: item.year,
                monthName: new Date(
                  item.year,
                  item.month - 1
                ).toLocaleDateString("en-US", {
                  month: "short",
                }),
                sortKey: item.year * 100 + item.month,
              }))
              .sort((a: any, b: any) => a.sortKey - b.sortKey);
            setMonthlyIncomeData(chartData);
          }
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
        setAverageMonthlyIncome((prev) => ({ ...prev, loading: false }));
      });

    fetchExpenseAnalytics();
  }, [token]);

  const fetchExpenseAnalytics = () => {
    setAnalyticsLoading(true);
    userExpenseService
      .getExpenseDashboardAnalytics(token)
      .then((data) => {
        console.log("Expense analytics data:", data);
        if (data && data.expense_analytics) {
          setExpenseAnalytics(data.expense_analytics);

          setTotalAlltimeExpenses(
            data.expense_analytics.total_alltime_expenses
          );

          setTotalExpensesTransactionsCount(
            data.expense_analytics.total_transactions_count
          );

          setTopExpenseCategory({
            expense_category_name:
              data.expense_analytics.top_expense_category.expense_category_name,
            total_amount:
              data.expense_analytics.top_expense_category.total_amount,
          });

          setCurrentMonthTotalExpenses(
            data.expense_analytics.current_month_total_expenses
          );

          // Process monthly expense data for the chart
          if (data.expense_analytics.expense_totals_by_month) {
            const chartData = data.expense_analytics.expense_totals_by_month
              .map((item: any) => ({
                month: `${item.month}/${item.year}`,
                total: item.total,
                year: item.year,
                monthName: new Date(
                  item.year,
                  item.month - 1
                ).toLocaleDateString("en-US", {
                  month: "short",
                }),
                sortKey: item.year * 100 + item.month,
              }))
              .sort((a: any, b: any) => a.sortKey - b.sortKey);
            setMonthlyExpenseData(chartData);
          }
        }
        setAnalyticsLoading(false);
      })
      .catch((error) => {
        console.error("Failed to fetch expense analytics:", error);
        toast.error("Failed to fetch expense analytics");
        setAnalyticsLoading(false);
      });
  };

  const fetchAccountBalance = () => {
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    analyticsService
      .getDashboardAnalytics(token)
      .then((data) => {
        if (data && data.user_credits) {
          setAccountBalance({
            amount: data.user_credits,
            loading: false,
          });

          setAccountNumber(data.account_number);
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
        setAccountBalance((prev) => ({ ...prev, loading: false }));
      });
  };

  const handleHealthCheck = () => {
    setHealthStatus((prev) => ({ ...prev, loading: true }));
    healthService
      .checkHealth()
      .then((data) => {
        setHealthStatus((prev) => ({
          ...prev,
          message: data.message,
          status: data.status,
          loading: false,
          time: new Date(),
        }));
      })
      .catch((error) => {
        toast.error("Failed to perform check!");
        setHealthStatus((prev) => ({ ...prev, loading: false }));
      });
  };

  return (
    <div className="flex flex-col gap-6">
      <p className="text-muted-foreground">
        Welcome back! Here's an overview of your account
      </p>

      {/* <DashboardStats /> */}

      {/* Income Analytics Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Income</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatAmount(totalAlltimeIncome, "KES")}
            </div>
            <p className="text-xs text-muted-foreground">All time total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Average
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatAmount(averageMonthlyIncome.amount, "KES")}
            </div>
            <p className="text-xs text-muted-foreground">Average per month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Transactions
            </CardTitle>
            <Hash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(totalTransactionsCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total Income transactions count
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Current Month Income
            </CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatAmount(currentMonthTotalIncome, "KES")}
            </div>
            <p className="text-xs text-muted-foreground">
              Current month total income
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Expense Analytics Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Expenses
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatAmount(totalAlltimeExpenses, "KES")}
            </div>
            <p className="text-xs text-muted-foreground">All time total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Average
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsLoading
                ? "Loading..."
                : expenseAnalytics
                ? sharedFunctions.formatAmount(
                    expenseAnalytics.average_monthly_expenses,
                    "KES"
                  )
                : "KES 0.00"}
            </div>
            <p className="text-xs text-muted-foreground">Average per month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Expenses Transactions
            </CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(totalExpensesTransactionsCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total expense transactions count
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Current Month Expenses
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatAmount(currentMonthTotalExpenses, "KES")}
            </div>
            <p className="text-xs text-muted-foreground">
              Current month expenses
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Income and Expense Trends Charts */}
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
        {/* Income Trends Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              Income Trends by Month
            </CardTitle>
          </CardHeader>
          <CardContent>
            {monthlyIncomeData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyIncomeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="monthName"
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: "#e0e0e0" }}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: "#e0e0e0" }}
                      tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                    />
                    <Tooltip
                      formatter={(value: any) => [
                        sharedFunctions.formatAmount(value, "KES"),
                        "Income",
                      ]}
                      labelFormatter={(label) => `Month: ${label}`}
                      contentStyle={{
                        backgroundColor: "#f8f9fa",
                        border: "1px solid #e0e0e0",
                        borderRadius: "8px",
                      }}
                    />
                    <Bar dataKey="total" fill="#16a34a" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-80 flex items-center justify-center">
                <p className="text-muted-foreground">
                  No monthly income data available
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Expense Trends Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-red-600" />
              Expense Trends by Month
            </CardTitle>
          </CardHeader>
          <CardContent>
            {monthlyExpenseData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyExpenseData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="monthName"
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: "#e0e0e0" }}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: "#e0e0e0" }}
                      tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                    />
                    <Tooltip
                      formatter={(value: any) => [
                        sharedFunctions.formatAmount(value, "KES"),
                        "Expense",
                      ]}
                      labelFormatter={(label) => `Month: ${label}`}
                      contentStyle={{
                        backgroundColor: "#f8f9fa",
                        border: "1px solid #e0e0e0",
                        borderRadius: "8px",
                      }}
                    />
                    <Bar dataKey="total" fill="#dc2626" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-80 flex items-center justify-center">
                <p className="text-muted-foreground">
                  No monthly expense data available
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Combined Income and Expense Trends Chart  */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            Combined Income vs Expense Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          {combinedChartData.length > 0 ? (
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={combinedChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="monthName"
                    tick={{ fontSize: 12 }}
                    tickLine={{ stroke: "#e0e0e0" }}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickLine={{ stroke: "#e0e0e0" }}
                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                  />
                  <Tooltip
                    formatter={(value: any, name: string) => [
                      sharedFunctions.formatAmount(value, "KES"),
                      name === "income" ? "Income" : "Expense",
                    ]}
                    labelFormatter={(label) => `Month: ${label}`}
                    contentStyle={{
                      backgroundColor: "#f8f9fa",
                      border: "1px solid #e0e0e0",
                      borderRadius: "8px",
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="income"
                    stroke="#16a34a"
                    strokeWidth={3}
                    dot={{ fill: "#16a34a", strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: "#16a34a", strokeWidth: 2 }}
                    name="Income"
                  />
                  <Line
                    type="monotone"
                    dataKey="expense"
                    stroke="#dc2626"
                    strokeWidth={3}
                    dot={{ fill: "#dc2626", strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: "#dc2626", strokeWidth: 2 }}
                    name="Expense"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-96 flex items-center justify-center">
              <p className="text-muted-foreground">
                No financial data available for comparison
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <Button asChild variant="outline">
              <Link href="/dashboard/user-incomes">View All Incomes</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/dashboard/user-incomes/new">Add New Income</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/dashboard/tax-categories">
                Manage Tax Categories
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/dashboard/user-expenses">View All Expenses</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/dashboard/user-expenses/new">Add New Expense</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/dashboard/expense-types">Manage Expense Types</Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Current Package
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Starter</div>
              {/* Last top-up: 100 credits on May 15, 2023 */}
              <p className="text-xs text-muted-foreground mt-1">
                For individuals
              </p>
              <Button
                size="sm"
                className="mt-4 bg-green-600 hover:bg-green-700"
              >
                <Link href="/dashboard/topup">Upgrade</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API Status</CardTitle>
              <div
                className={cn(
                  "h-3 w-3 rounded-full",
                  healthStatus.status === "Ok" ? "bg-green-500" : "bg-red-500"
                )}
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{healthStatus.message}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Last check: {format(healthStatus.time, "dd MMM yyyy")} at{" "}
                {format(healthStatus.time, "hh:mm a")}
              </p>
              <Button
                size="sm"
                className="mt-4 bg-green-600 hover:bg-green-700"
                onClick={handleHealthCheck}
                disabled={healthStatus.loading}
              >
                {healthStatus.loading ? "Checking..." : "Check Again"}
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Account Number
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{accountNumber}</div>
              {/* Last top-up: 100 credits on May 15, 2023 */}
              <p className="text-xs text-muted-foreground mt-1">
                For credit top ups. Paybill 4044085.
              </p>
              <Button
                size="sm"
                className="mt-4 bg-green-600 hover:bg-green-700"
              >
                <Link href="/dashboard/topup">Top Up Credits</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* <Tabs defaultValue="decode">
        <TabsList>
          <TabsTrigger value="decode">Decode Hash</TabsTrigger>
          <TabsTrigger value="generate">Generate Hash</TabsTrigger>
        </TabsList>
        <TabsContent value="decode" className="space-y-4"> 
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <SingleHashDecoder onDecodeSuccess={handleDecodedTransaction} />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="generate" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <SingleHashGenerator
                onGenerateSuccess={handleDecodedTransaction}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs> */}
    </div>
  );
}
