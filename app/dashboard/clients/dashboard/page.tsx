"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { <PERSON>, Card<PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DollarSign,
  TrendingUp,
  Building,
  Hash,
  Users,
  BarChart3,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { analyticsService } from "@/services/api";
import { useAuth } from "@/context/auth-context";
import Link from "next/link";
import { sharedFunctions } from "@/services/shared_functions";

export default function DashboardPage() {
  const { token } = useAuth();
  const [averageMonthlyIncome, setAverageMonthlyIncome] = useState({
    amount: 0.0,
    loading: false,
  });

  const [highestPayingOrganization, setHighestPayingOrganization] = useState({
    organization_name: "",
    total_amount: 0.0,
  });

  const [totalTransactionsCount, setTotalTransactionsCount] = useState(0);
  const [highestPayingTaxCategory, setHighestPayingTaxCategory] = useState({
    tax_category_name: "",
    total_amount: 0.0,
  });
  const [currentMonthTotalIncome, setCurrentMonthTotalIncome] = useState(0.0);
  const [monthlyIncomeData, setMonthlyIncomeData] = useState<any[]>([]);

  const [allCustomersCount, setAllCustomersCount] = useState(0);

  useEffect(() => {
    fetchDashboardAnalytics();
  }, [token]);

  const fetchDashboardAnalytics = () => {
    setAverageMonthlyIncome((prev) => ({ ...prev, loading: true }));
    setAverageMonthlyIncome((prev) => ({ ...prev, loading: true }));

    console.log("fetching dashboard analytics...");

    analyticsService
      .getDashboardAnalytics(token)
      .then((data) => {
        if (data && data.income_analytics) {
          setAllCustomersCount(data.all_customers_count);
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
        setAverageMonthlyIncome((prev) => ({ ...prev, loading: false }));
      });
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Income Dashboard
          </h1>
          <p className="text-muted-foreground">
            Track and analyze your income patterns
          </p>
        </div>
        <Button asChild className="bg-green-600 hover:bg-green-700">
          <Link href="/dashboard/user-incomes/new">Add New Income</Link>
        </Button>
      </div>

      {/* Income Analytics Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              All Customers Count
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(allCustomersCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              All registered customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Average
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatAmount(averageMonthlyIncome.amount, "KES")}
            </div>
            <p className="text-xs text-muted-foreground">Average per month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Transactions
            </CardTitle>
            <Hash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatNumber(totalTransactionsCount)}
            </div>
            <p className="text-xs text-muted-foreground">Income transactions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sharedFunctions.formatAmount(currentMonthTotalIncome, "KES")}
            </div>
            <p className="text-xs text-muted-foreground">
              Current month total income
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Income Breakdown Sections */}
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
        {/* Top Organizations */}
        <Card>
          <CardHeader>
            <CardTitle>Top Organization</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">
                    {highestPayingOrganization.organization_name ||
                      "No organization data"}
                  </span>
                </div>
                <span className="text-sm font-bold">
                  {sharedFunctions.formatAmount(
                    highestPayingOrganization.total_amount,
                    "KES"
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tax Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Top Tax Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium">
                    {highestPayingTaxCategory.tax_category_name ||
                      "No category data"}
                  </span>
                </div>
                <span className="text-sm font-bold">
                  {sharedFunctions.formatAmount(
                    highestPayingTaxCategory.total_amount,
                    "KES"
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Income Trends Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Income Trends by Month
          </CardTitle>
        </CardHeader>
        <CardContent>
          {monthlyIncomeData.length > 0 ? (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyIncomeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="monthName"
                    tick={{ fontSize: 12 }}
                    tickLine={{ stroke: "#e0e0e0" }}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickLine={{ stroke: "#e0e0e0" }}
                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                  />
                  <Tooltip
                    formatter={(value: any) => [
                      sharedFunctions.formatAmount(value, "KES"),
                      "Income",
                    ]}
                    labelFormatter={(label) => `Month: ${label}`}
                    contentStyle={{
                      backgroundColor: "#f8f9fa",
                      border: "1px solid #e0e0e0",
                      borderRadius: "8px",
                    }}
                  />
                  <Bar dataKey="total" fill="#16a34a" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-80 flex items-center justify-center">
              <p className="text-muted-foreground">
                No monthly income data available
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <Button asChild variant="outline">
              <Link href="/dashboard/user-incomes">View All Incomes</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/dashboard/user-incomes/new">Add New Income</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/dashboard/tax-categories">
                Manage Tax Categories
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
