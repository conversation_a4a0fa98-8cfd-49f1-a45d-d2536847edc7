"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/context/auth-context";
import { toast } from "sonner";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { clientService } from "@/services/client_service";
import { Client } from "@/types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import {
  ArrowLeft,
  Edit,
  Banknote,
  Calendar,
  CreditCard,
  Store,
  FileText,
  Download,
  ExternalLink,
} from "lucide-react";

import { sharedFunctions } from "@/services/shared_functions";
import { format } from "date-fns";

export default function ViewClientPage() {
  const { token } = useAuth();
  const { id } = useParams<{ id: string }>();
  const [taxCategoryId, setTaxCategoryId] = useState(3);
  const [amount, setAmount] = useState(0);
  const [incomeDate, setIncomeDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [merchantName, setMerchantName] = useState("");
  const [merchantPin, setMerchantPin] = useState("");
  const [paymentFrequency, setPaymentFrequency] = useState("monthly");
  const [description, setDescription] = useState("");
  const [createdAt, setCreatedAt] = useState(new Date());

  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [client, setClient] = useState<Client | null>(null);

  const [activeTab, setActiveTab] = useState("overview");

  const router = useRouter();

  useEffect(() => {
    clientService
      .getClientById(Number(id), token)
      .then((res) => {
        console.log(
          "retrieved client, setting values..., res= " + JSON.stringify(res)
        );

        console.log("res.client= " + JSON.stringify(res));

        // Convert res to client
        const client = res;
        console.log("client= " + JSON.stringify(client));

        setClient(res);

        setCreatedAt(new Date(res.created_at));
      })
      .catch((err) => {
        console.log("Failed to fetch client: " + err);
        toast.error("Error!", {
          description: "Failed to fetch client.",
        });
      });
  }, [token]);

  const updateClient = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    var clientId = Number(id);

    try {
      const result = await clientService.updateClient(
        clientId,
        {
          amount,
          currency: "KES",
          description,
          tax_category_id: taxCategoryId,
          income_date: incomeDate,
          business_name: merchantName,
          merchant_pin: merchantPin,
          payment_frequency: "monthly",
          start_date: "",
          end_date: "",
          withholding_tax_rate: 0,
          withholding_tax_amount: 0,
        },
        token
      );

      if (result.id > 0) {
        router.push("/dashboard/clients");
      } else {
        setError("Failed to update income. Please try again.");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/dashboard/user-incomes">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">{client?.business_name}</h1>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/dashboard/clients/`}>
              <Banknote className="mr-2 h-4 w-4" /> View Clients
            </Link>
          </Button>
          {/* <Button variant="outline" asChild>
            <Link href={`/dashboard/clients/${id}/edit`}>
              <Edit className="mr-2 h-4 w-4" /> Edit Income
            </Link>
          </Button> */}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Company Details</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="merchant">Director</TabsTrigger>
                  <TabsTrigger value="attachment">Attachments</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4 mt-4">
                  <h3 className="font-semibold text-lg">Business Name</h3>
                  <p className="text-gray-700">{client?.business_name}</p>

                  <h3 className="font-semibold text-lg">Business PIN</h3>
                  <p className="text-gray-700">{client?.business_pin}</p>

                  <h3 className="font-semibold text-lg">Company Address</h3>
                  <p className="text-gray-700">{client?.company_address}</p>

                  <h3 className="font-semibold text-lg">Company Email</h3>
                  <p className="text-gray-700">{client?.company_email}</p>

                  <h3 className="font-semibold text-lg">
                    Company Phone Number
                  </h3>
                  <p className="text-gray-700">
                    {client?.company_phone_number}
                  </p>
                </TabsContent>

                <TabsContent value="merchant" className="space-y-4 mt-4">
                  <h3 className="font-semibold text-lg">Director Name</h3>
                  <p className="text-gray-700">{client?.director_name}</p>

                  <h3 className="font-semibold text-lg">Director PIN</h3>
                  <p className="text-gray-700">{client?.director_pin}</p>

                  <h3 className="font-semibold text-lg">
                    Director Phone Number
                  </h3>
                  <p className="text-gray-700">
                    {client?.director_phone_number}
                  </p>

                  <h3 className="font-semibold text-lg">Director Email</h3>
                  <p className="text-gray-700">{client?.director_email}</p>
                </TabsContent>

                <TabsContent value="attachment" className="space-y-4 mt-4">
                  <h3 className="font-semibold text-lg">Attachment</h3>

                  {client?.client_assets && client.client_assets.length > 0 ? (
                    <div className="space-y-4">
                      {client.client_assets.map((incomeAsset) => {
                        // Extract filename from URL if possible
                        const urlParts = incomeAsset.asset.url.split("/");
                        const filename =
                          urlParts[urlParts.length - 1] ||
                          `attachment-${incomeAsset.asset.id}`;

                        return (
                          <div
                            key={incomeAsset.asset.id}
                            className="border rounded-lg p-4 bg-gray-50"
                          >
                            <div className="flex items-center gap-3 mb-3">
                              <FileText className="h-5 w-5 text-blue-600" />
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">
                                  {filename}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Uploaded on{" "}
                                  {new Date(
                                    incomeAsset.asset.created_at
                                  ).toLocaleDateString()}
                                </p>
                              </div>
                            </div>

                            <div className="flex gap-2">
                              <Button variant="outline" size="sm" asChild>
                                <a
                                  href={incomeAsset.asset.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center gap-2"
                                >
                                  <ExternalLink className="h-4 w-4" />
                                  View
                                </a>
                              </Button>

                              <Button variant="outline" size="sm" asChild>
                                <a
                                  href={incomeAsset.asset.url}
                                  download={filename}
                                  className="flex items-center gap-2"
                                >
                                  <Download className="h-4 w-4" />
                                  Download
                                </a>
                              </Button>
                            </div>

                            {/* Inline attachment preview */}
                            <div className="mt-4 border-t pt-4">
                              <div className="w-full">
                                {/* Check if it's an image */}
                                {incomeAsset.asset.url.match(
                                  /\.(jpg|jpeg|png|gif|bmp|webp)$/i
                                ) ? (
                                  <div className="space-y-2">
                                    <p className="text-sm font-medium text-gray-700">
                                      Preview:
                                    </p>
                                    <img
                                      src={incomeAsset.asset.url}
                                      alt={filename}
                                      className="max-w-full h-auto max-h-96 rounded-lg border shadow-sm"
                                      onError={(e) => {
                                        e.currentTarget.style.display = "none";
                                      }}
                                    />
                                  </div>
                                ) : incomeAsset.asset.url.match(/\.pdf$/i) ? (
                                  /* PDF preview */
                                  <div className="space-y-2">
                                    <p className="text-sm font-medium text-gray-700">
                                      PDF Preview:
                                    </p>
                                    <iframe
                                      src={incomeAsset.asset.url}
                                      className="w-full h-96 border rounded-lg"
                                      title={`PDF Preview: ${filename}`}
                                    />
                                  </div>
                                ) : (
                                  /* Other file types - show a message */
                                  <div className="text-center py-4 text-gray-500">
                                    <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                                    <p className="text-sm">
                                      Preview not available for this file type.
                                    </p>
                                    <p className="text-xs">
                                      Use the View button to open the file.
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                      <p>No attachments found for this income.</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Banknote className="h-4 w-4 text-gray-500" />
                <span>{client?.business_name}</span>
              </div>

              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-gray-500" />
                <span>Company PIN: {client?.business_pin}</span>
              </div>

              <div className="flex items-center gap-2">
                <Store className="h-4 w-4 text-gray-500" />
                <span>Director: {client?.director_name}</span>
              </div>

              <div className="flex items-center gap-2">
                <Store className="h-4 w-4 text-gray-500" />
                <span>Director PIN: {client?.director_pin}</span>
              </div>

              <div className="flex items-center gap-2">
                <Store className="h-4 w-4 text-gray-500" />
                <span>Director Phone: {client?.director_phone_number}</span>
              </div>

              <div className="flex items-center gap-2">
                <Store className="h-4 w-4 text-gray-500" />
                <span>Filled By: {client?.form_filled_by}</span>
              </div>

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span>Created On: {format(createdAt, "dd MMM yyyy")}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
