"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/context/auth-context";
import { useParams, useRouter } from "next/navigation";
import { userIncomeService } from "@/services/user_income_service";
import { toast } from "sonner";
import Link from "next/link";
import { TaxCategory, UserIncome } from "@/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function EditIncomeSourcePage() {
  const { token } = useAuth();
  const { id } = useParams<{ id: string }>();
  const [taxCategories, setTaxCategories] = useState<TaxCategory[] | []>([]);

  const router = useRouter();

  const [taxCategoryId, setTaxCategoryId] = useState(3);
  const [amount, setAmount] = useState(0);
  const [incomeDate, setIncomeDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [merchantName, setMerchantName] = useState("");
  const [merchantPin, setMerchantPin] = useState("");
  const [description, setDescription] = useState("");
  const [paymentFrequency, setPaymentFrequency] = useState("monthly");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [withholdingTaxRate, setWithholdingTaxRate] = useState(0);
  const [withholdingTaxAmount, setWithholdingTaxAmount] = useState(0);
  const [createdAt, setCreatedAt] = useState(new Date());

  const [userIncome, setUserIncome] = useState<UserIncome | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    userIncomeService
      .getUserIncomeById(Number(id), token)
      .then((res) => {
        console.log(
          "retrieved user income, setting values..., res= " +
            JSON.stringify(res)
        );

        console.log("res.userIncome= " + JSON.stringify(res));

        // Convert res to userIncome
        const userIncome = res;
        console.log("userIncome= " + JSON.stringify(userIncome));

        setUserIncome(res);
        setTaxCategoryId(res.tax_category_id);
        setAmount(res.amount);

        // Convert expense_date to YYYY-MM-DD format for date input
        if (res.income_date) {
          console.log("Original income_date from API:", res.income_date);
          const incomeDate = new Date(res.income_date);
          const formattedDate = incomeDate.toISOString().split("T")[0];
          console.log("Formatted income_date for input:", formattedDate);
          setIncomeDate(formattedDate);
        }

        setMerchantName(res.merchant_name);
        setMerchantPin(res.merchant_pin);
        setPaymentFrequency(res.payment_frequency);
        setDescription(res.description);
        setCreatedAt(new Date(res.created_at));

        // Convert date strings to YYYY-MM-DD format for date inputs
        if (res.start_date) {
          const startDate = new Date(res.start_date);
          setStartDate(startDate.toISOString().split("T")[0]);
        }
        if (res.end_date) {
          const endDate = new Date(res.end_date);
          setEndDate(endDate.toISOString().split("T")[0]);
        }

        setWithholdingTaxRate(res.withholding_tax_rate);
        setWithholdingTaxAmount(res.withholding_tax_amount);
      })
      .catch((err) => {
        console.log("Failed to fetch user income: " + err);
        toast.error("Error!", {
          description: "Failed to fetch user income.",
        });
      });
  }, [token]);

  const updateUserIncome = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    // Validate required fields
    if (!amount || amount <= 0) {
      setError("Amount is required and must be greater than 0.");
      setIsSubmitting(false);
      return;
    }

    if (!merchantName || merchantName.trim() === "") {
      setError("Merchant name is required.");
      setIsSubmitting(false);
      return;
    }

    var userIncomeId = Number(id);

    try {
      const result = await userIncomeService.updateUserIncome(
        userIncomeId,
        {
          amount,
          currency: "KES",
          description,
          tax_category_id: taxCategoryId,
          income_date: incomeDate,
          merchant_name: merchantName,
          merchant_pin: merchantPin,
          payment_frequency: paymentFrequency,
          start_date: startDate,
          end_date: endDate,
          withholding_tax_rate: withholdingTaxRate,
          withholding_tax_amount: withholdingTaxAmount,
        },
        token
      );

      if (result.id > 0) {
        router.push("/dashboard/user-incomes");
      } else {
        setError("Failed to update user expense. Please try again.");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWithholdingTaxRateChange = (withholdingTaxRate: number) => {
    setWithholdingTaxRate(withholdingTaxRate);

    var withholdingTaxAmount = (amount * withholdingTaxRate) / 100;
    setWithholdingTaxAmount(withholdingTaxAmount);
  };
  return (
    <div className="">
      {/* <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Create Income Source</h1>
        <p className="text-muted-foreground">
          Create income source.
        </p>
      </div> */}

      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Update client</CardTitle>
            <CardDescription>Update client</CardDescription>
          </CardHeader>

          <form onSubmit={updateUserIncome}>
            <CardContent className="space-y-8">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiry">Income Source</Label>
                  <Select
                    defaultValue="3"
                    onValueChange={(e) => setTaxCategoryId(Number(e))}
                    value={taxCategoryId.toString()}
                  >
                    <SelectTrigger id="report-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {taxCategories.map((taxCategory: TaxCategory) => (
                        <SelectItem
                          key={taxCategory.id}
                          value={taxCategory.id.toString()}
                        >
                          {taxCategory.income_source}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="income_date">Income Date</Label>
                  <Input
                    id="income_date"
                    placeholder="Income Date"
                    type="date"
                    value={incomeDate}
                    onChange={(e) => setIncomeDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiry">Organization name</Label>
                  <Input
                    id="expiry"
                    placeholder="Organization name"
                    value={merchantName}
                    onChange={(e) => setMerchantName(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="merchant_kra_pin">Organization KRA PIN</Label>
                  <Input
                    id="merchant_kra_pin"
                    placeholder="Organization KRA PIN"
                    value={merchantPin}
                    onChange={(e) => setMerchantPin(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Income Description</Label>
                <Textarea
                  id="description"
                  placeholder="Income description"
                  className="min-h-[100px]"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

              {/* <div className="grid grid-cols-3 gap-4"> */}

              {/* <div className="space-y-2">
                      <Label htmlFor="start_date">Start Date</Label>
                      <Input 
                        id="start_date" 
                        type="date"
                        placeholder="Start date" 
                        value={startDate || ""}
                        onChange={(e) => setStartDate(e.target.value)}
                      />
                    </div> */}
              {/* <div className="space-y-2">
                      <Label htmlFor="end_date">End Date</Label>
                      <Input 
                        id="end_date" 
                        type="date"
                        placeholder="End date" 
                        value={endDate || ""}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                    </div> */}
              {/* </div> */}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="amount">Income (Kshs): (Gross)</Label>
                  <Input
                    id="amount"
                    type="number"
                    min="0.01"
                    step="0.01"
                    placeholder="Amount (Kshs)"
                    value={amount || ""}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expiry">Payment Frequency</Label>
                  {/* <Input id="expiry" placeholder="Payment frequency" /> */}
                  <Select
                    defaultValue="monthly"
                    onValueChange={(e) => setPaymentFrequency(e)}
                    value={paymentFrequency}
                  >
                    <SelectTrigger id="report-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="withholding_tax_rate">
                    Withholding Tax Rate
                  </Label>
                  <Input
                    id="withholding_tax_rate"
                    type="number"
                    placeholder="Withholding Tax Rate"
                    value={withholdingTaxRate}
                    onChange={(e) =>
                      handleWithholdingTaxRateChange(Number(e.target.value))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="withhoding_tax_amount">
                    Withholding Tax Amount
                  </Label>
                  <Input
                    id="withhoding_tax_amount"
                    type="number"
                    placeholder="Withholding Amount"
                    value={withholdingTaxAmount}
                    onChange={(e) =>
                      setWithholdingTaxAmount(Number(e.target.value))
                    }
                  />
                </div>
              </div>
            </CardContent>

            {/* <CardFooter> */}
            {/* <Button className="w-full bg-green-600 hover:bg-green-700">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Create Income Source
                    </Button> */}

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline">
                <Link href="/dashboard/user-incomes">Cancel</Link>
              </Button>
              <Button
                type="submit"
                disabled={
                  isSubmitting ||
                  !amount ||
                  amount <= 0 ||
                  !merchantName ||
                  merchantName.trim() === ""
                }
              >
                Update Income Source
              </Button>{" "}
              &nbsp; &nbsp; &nbsp;
            </div>
          </form>

          <br />

          {/* </CardFooter> */}
        </Card>
      </div>

      {/* </div> */}
    </div>
  );
}
