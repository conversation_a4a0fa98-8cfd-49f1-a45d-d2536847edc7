"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/context/auth-context";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import Link from "next/link";
import { userIncomeService } from "@/services/user_income_service";
import { TaxCategory } from "@/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { taxCategoryService } from "@/services/tax_category_service";

export default function CreateIncomeSourcePage() {
  const { token } = useAuth();
  const [taxCategories, setTaxCategories] = useState<TaxCategory[] | []>([]);

  const router = useRouter();

  const [taxCategoryId, setTaxCategoryId] = useState(3);
  const [amount, setAmount] = useState(0);
  const [incomeDate, setIncomeDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [merchantName, setMerchantName] = useState("");
  const [merchantPin, setMerchantPin] = useState("");
  const [description, setDescription] = useState("");
  const [paymentFrequency, setPaymentFrequency] = useState("monthly");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [withholdingTaxRate, setWithholdingTaxRate] = useState(0);
  const [withholdingTaxAmount, setWithholdingTaxAmount] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    taxCategoryService
      .filterTaxCategories(token)
      .then((res) => {
        setTaxCategories(res.tax_categories);
      })
      .catch((err) => {
        toast.error("Error!", {
          description: "Failed to fetch tax categories.",
        });
      });
  }, [token]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setSelectedFile(file || null);
  };

  const createUserIncome = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    // Validate required fields
    if (!amount || amount <= 0) {
      setError("Amount is required and must be greater than 0.");
      setIsSubmitting(false);
      return;
    }

    if (!merchantName || merchantName.trim() === "") {
      setError("Merchant name is required.");
      setIsSubmitting(false);
      return;
    }

    // Convert selectedFile to Base64 and populate attachment
    var attachment = {
      async: false,
      content_md5: "",
      content_size: selectedFile ? selectedFile.size : 0,
      content_type: selectedFile ? selectedFile.type : "",
      duration: 0,
      data: "",
    };

    // Convert file to Base64 and calculate MD5 if a file is selected
    if (selectedFile) {
      try {
        const base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            // Remove the data URL prefix (e.g., "data:image/jpeg;base64,")
            const base64 = result.split(",")[1];
            resolve(base64);
          };
          reader.onerror = reject;
          reader.readAsDataURL(selectedFile);
        });

        // Calculate MD5 hash of the file using a simple implementation
        // Note: Using SHA-256 as MD5 is not available in Web Crypto API
        const fileBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as ArrayBuffer);
          reader.onerror = reject;
          reader.readAsArrayBuffer(selectedFile);
        });

        // Generate SHA-256 hash (more secure than MD5) and truncate to MD5 length for compatibility
        const hashBuffer = await crypto.subtle.digest("SHA-256", fileBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const fullHash = hashArray
          .map((b) => b.toString(16).padStart(2, "0"))
          .join("");
        // Truncate to 32 characters to match MD5 length
        const md5Hash = fullHash.substring(0, 32);

        attachment.data = base64Data;
        attachment.content_md5 = md5Hash;
      } catch (error) {
        console.error("Error processing file:", error);
        setError("Failed to process the selected file. Please try again.");
        setIsSubmitting(false);
        return;
      }
    }

    try {
      const result = await userIncomeService.createUserIncome(
        {
          attachment: attachment,
          tax_category_id: taxCategoryId,
          income_date: incomeDate,
          merchant_name: merchantName,
          merchant_pin: merchantPin,
          amount,
          currency: "KES",
          description,
          payment_frequency: paymentFrequency,
          start_date: startDate,
          end_date: endDate,
          withholding_tax_rate: withholdingTaxRate,
          withholding_tax_amount: withholdingTaxAmount,
        },
        token
      );

      if (result.id > 0) {
        router.push("/dashboard/user-incomes");
      } else {
        setError("Failed to create income. Please try again.");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWithholdingTaxRateChange = (withholdingTaxRate: number) => {
    setWithholdingTaxRate(withholdingTaxRate);

    var withholdingTaxAmount = (amount * withholdingTaxRate) / 100;
    setWithholdingTaxAmount(withholdingTaxAmount);
  };

  return (
    <div className="">
      {/* <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Create Income Source</h1>
        <p className="text-muted-foreground">
          Create income source.
        </p>
      </div> */}

      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Add New Client</CardTitle>
            <CardDescription>Register a new client</CardDescription>
          </CardHeader>

          <form onSubmit={createUserIncome}>
            <CardContent className="space-y-8">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiry">Income Source</Label>
                  <Select defaultValue="3">
                    <SelectTrigger id="report-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {taxCategories.map((taxCategory: TaxCategory) => (
                        <SelectItem
                          key={taxCategory.id}
                          value={taxCategory.id.toString()}
                        >
                          {taxCategory.income_source}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="income_date">Income Date</Label>
                  <Input
                    id="income_date"
                    placeholder="Income Date"
                    type="date"
                    value={incomeDate}
                    onChange={(e) => setIncomeDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="organization_name">Organization name</Label>
                  <Input
                    id="organization_name"
                    placeholder="Organization name"
                    value={merchantName}
                    onChange={(e) => setMerchantName(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="organization_name">
                    Organization KRA PIN
                  </Label>
                  <Input
                    id="organization_name"
                    placeholder="Organization KRA PIN"
                    value={merchantPin}
                    onChange={(e) => setMerchantPin(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Income Description</Label>
                <Textarea
                  id="description"
                  placeholder="Income description"
                  className="min-h-[100px]"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

              {/* <div className="grid grid-cols-3 gap-4"> */}

              {/* <div className="space-y-2">
                      <Label htmlFor="start_date">Start Date</Label>
                      <Input 
                        id="start_date" 
                        type="date"
                        placeholder="Start date" 
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                      />
                    </div> */}
              {/* <div className="space-y-2">
                      <Label htmlFor="end_date">End Date</Label>
                      <Input 
                        id="end_date" 
                        type="date"
                        placeholder="End date" 
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                    </div> */}
              {/* </div> */}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="amount">Income (Kshs): (Gross)</Label>
                  <Input
                    id="amount"
                    type="number"
                    min="0.01"
                    step="0.01"
                    placeholder="Amount (Kshs)"
                    value={amount || ""}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expiry">Payment Frequency</Label>
                  {/* <Input id="expiry" placeholder="Payment frequency" /> */}
                  <Select
                    defaultValue="monthly"
                    onValueChange={(e) => setPaymentFrequency(e)}
                    value={paymentFrequency}
                  >
                    <SelectTrigger id="report-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="withholding_tax_rate">
                    Withholding Tax Rate
                  </Label>
                  <Input
                    id="withholding_tax_rate"
                    placeholder="Withholding Tax Rate"
                    type="number"
                    onChange={(e) =>
                      handleWithholdingTaxRateChange(Number(e.target.value))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="withholding_tax_amount">
                    Withholding Amount
                  </Label>
                  <Input
                    id="withholding_tax_amount"
                    placeholder="Withholding Amount"
                    type="number"
                    value={withholdingTaxAmount || ""}
                    onChange={(e) =>
                      setWithholdingTaxAmount(Number(e.target.value))
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="file_upload">Upload Attachment</Label>
                  <Input
                    id="file_upload"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx"
                    onChange={handleFileChange}
                    className="cursor-pointer"
                  />
                  {selectedFile && (
                    <p className="text-sm text-gray-600 mt-1">
                      Selected file:{" "}
                      <span className="font-medium">{selectedFile.name}</span>
                    </p>
                  )}
                </div>
              </div>
            </CardContent>

            {/* <CardFooter> */}
            {/* <Button className="w-full bg-green-600 hover:bg-green-700">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Create Income Source
                  </Button> */}

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline">
                <Link href="/dashboard/user-incomes">Cancel</Link>
              </Button>
              <Button type="submit">Create Income Source</Button> &nbsp; &nbsp;
              &nbsp;
            </div>
          </form>

          <br />

          {/* </CardFooter> */}
        </Card>
      </div>

      {/* </div> */}
    </div>
  );
}
