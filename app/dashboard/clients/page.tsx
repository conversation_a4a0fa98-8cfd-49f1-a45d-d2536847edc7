"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useAuth } from "@/context/auth-context";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Client } from "@/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MoreVertical, Search, Plus, Eye, Edit } from "lucide-react";
import Link from "next/link";
import { sharedFunctions } from "@/services/shared_functions";
import { clientService } from "@/services/client_service";

export default function IncomeTrackerPage() {
  const { token } = useAuth();
  const [clients, setClients] = useState<Client[] | []>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (token) {
      setIsLoading(true);

      clientService
        .filterClients(token)
        .then((res) => {
          setClients(res.clients || []);
        })
        .catch((err) => {
          console.error("Error fetching clients:", err);
          setClients([]);
          toast.error("Error!", {
            description: "Failed to fetch clients.",
          });
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [token]);

  // Implement search function using search term
  const searchClients = (searchTerm: string) => {
    console.log("searchTerm: " + searchTerm);
    setSearchTerm(searchTerm);
    if (token) {
      setIsLoading(true);
      clientService
        .searchClients(token, searchTerm)
        .then((res) => {
          setClients(res.clients || []);
        })
        .catch((err) => {
          console.error("Error searching clients:", err);
          setClients([]);
          toast.error("Error!", {
            description: "Failed to search clients.",
          });
        });
    }
  };

  return (
    <div className="flex flex-col gap-6 overflow-hidden">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Clients</h1>
        {/* <Button asChild>
          <Link href="/dashboard/user-incomes/new">
            <Plus className="mr-2 h-4 w-4" /> Add Client
          </Link>
        </Button> */}
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search clients..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => searchClients(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Business Name</TableHead>
              <TableHead>Business PIN</TableHead>
              <TableHead>Company Email</TableHead>
              <TableHead>Director Name</TableHead>
              <TableHead>Filled By</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {clients &&
              clients.length > 0 &&
              clients.map((client: Client) => (
                <TableRow key={client.id}>
                  <TableCell className="font-medium">
                    {client.business_name}
                  </TableCell>
                  <TableCell>{client.business_pin}</TableCell>
                  <TableCell>{client.company_email}</TableCell>
                  <TableCell>{client.director_name}</TableCell>
                  <TableCell>{client.form_filled_by}</TableCell>
                  <TableCell>
                    {format(client.created_at, "dd MMM yyyy")}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/clients/${client.id}`}>
                            <Eye className="mr-2 h-4 w-4" /> View
                          </Link>
                        </DropdownMenuItem>
                        {/* <DropdownMenuItem asChild>
                          <Link href={`/dashboard/clients/${client.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" /> Edit
                          </Link>
                        </DropdownMenuItem> */}
                        <DropdownMenuSeparator />
                        {/* <DropdownMenuItem className="text-destructive">
                          <Trash className="mr-2 h-4 w-4" /> Delete
                        </DropdownMenuItem> */}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            {(!clients || clients.length === 0) && !isLoading && (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className="text-center py-6 text-muted-foreground"
                >
                  No incomes found
                </TableCell>
              </TableRow>
            )}
            {(!clients || clients.length === 0) && isLoading && (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className="text-center py-6 text-muted-foreground"
                >
                  Loading incomes...
                </TableCell>
              </TableRow>
            )}
            {/* {filteredJobs.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={7}
                    className="text-center py-6 text-muted-foreground"
                  >
                    No jobs found matching your search criteria
                  </TableCell>
                </TableRow>
              )} */}
          </TableBody>
        </Table>

        {/* <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="filled">Filled</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
            </SelectContent>
          </Select> */}
      </div>
    </div>
  );
}
