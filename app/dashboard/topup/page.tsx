"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CreditCard, Smartphone } from "lucide-react";
import { analyticsService } from "@/services/api";
import { useAuth } from "@/context/auth-context";
import { toast } from "sonner";

export default function TopupPage() {
  const { token } = useAuth();
  const [accountBalance, setAccountBalance] = useState({
    amount: 245,
    loading: false,
  });

  useEffect(() => {
    fetchAccountBalance();
  }, [token]);

  const fetchAccountBalance = () => {
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    analyticsService
      .getDashboardAnalytics(token)
      .then((data) => {
        if (data && data.user_credits) {
          setAccountBalance({
            amount: data.user_credits,
            loading: false,
          });
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
        setAccountBalance((prev) => ({ ...prev, loading: false }));
      });
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Top-up Credits</h1>
        <p className="text-muted-foreground">
          Purchase additional credits to continue using our Phone hash decoding
          services.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Current Balance</CardTitle>
              <CardDescription>
                Your current credit balance and usage information.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col space-y-1">
                <span className="text-sm text-muted-foreground">
                  Available Credits
                </span>
                <span className="text-3xl font-bold">
                  {accountBalance.loading
                    ? "Loading..."
                    : `${accountBalance.amount.toFixed(2)} Credits`}
                </span>
              </div>
              <div className="flex flex-col space-y-1">
                <span className="text-sm text-muted-foreground">
                  Credits Used (This Month)
                </span>
                <span className="text-xl">55 Credits</span>
              </div>
              <div className="flex flex-col space-y-1">
                <span className="text-sm text-muted-foreground">
                  Credits Added (This Month)
                </span>
                <span className="text-xl">300 Credits</span>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Tabs defaultValue="mpesa">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="mpesa">M-Pesa</TabsTrigger>
              <TabsTrigger value="card">Credit Card</TabsTrigger>
            </TabsList>
            <TabsContent value="mpesa">
              <Card>
                <CardHeader>
                  <CardTitle>Top-up via M-Pesa</CardTitle>
                  <CardDescription>
                    Purchase credits using M-Pesa mobile money.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <RadioGroup defaultValue="package1">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="package1" id="package1" />
                      <Label
                        htmlFor="package1"
                        className="flex justify-between w-full"
                      >
                        <span>2000 Credits</span>
                        <span className="font-semibold">KSh 1,000</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="package2" id="package2" />
                      <Label
                        htmlFor="package2"
                        className="flex justify-between w-full"
                      >
                        <span>10000 Credits</span>
                        <span className="font-semibold">KSh 4,500</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="package3" id="package3" />
                      <Label
                        htmlFor="package3"
                        className="flex justify-between w-full"
                      >
                        <span>20000 Credits</span>
                        <span className="font-semibold">KSh 8,000</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="custom" id="custom" />
                      <Label
                        htmlFor="custom"
                        className="flex justify-between w-full"
                      >
                        <span>Custom Amount</span>
                      </Label>
                    </div>
                  </RadioGroup>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" placeholder="Enter your M-Pesa number" />
                    <p className="text-xs text-muted-foreground">
                      Enter the phone number registered with M-Pesa.
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    <Smartphone className="mr-2 h-4 w-4" />
                    Pay with M-Pesa
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            <TabsContent value="card">
              <Card>
                <CardHeader>
                  <CardTitle>Top-up via Credit Card</CardTitle>
                  <CardDescription>
                    Purchase credits using your credit or debit card.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <RadioGroup defaultValue="package1">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="package1" id="cc-package1" />
                      <Label
                        htmlFor="cc-package1"
                        className="flex justify-between w-full"
                      >
                        <span>2000 Credits</span>
                        <span className="font-semibold">KSh 1,000</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="package2" id="cc-package2" />
                      <Label
                        htmlFor="cc-package2"
                        className="flex justify-between w-full"
                      >
                        <span>10000 Credits</span>
                        <span className="font-semibold">KSh 4,500</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="package3" id="cc-package3" />
                      <Label
                        htmlFor="cc-package3"
                        className="flex justify-between w-full"
                      >
                        <span>20000 Credits</span>
                        <span className="font-semibold">KSh 8,000</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="custom" id="cc-custom" />
                      <Label
                        htmlFor="cc-custom"
                        className="flex justify-between w-full"
                      >
                        <span>Custom Amount</span>
                      </Label>
                    </div>
                  </RadioGroup>

                  <div className="space-y-2">
                    <Label htmlFor="card-number">Card Number</Label>
                    <Input
                      id="card-number"
                      placeholder="Enter your card number"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiry">Expiry Date</Label>
                      <Input id="expiry" placeholder="MM/YY" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cvv">CVV</Label>
                      <Input id="cvv" placeholder="CVV" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">Name on Card</Label>
                    <Input id="name" placeholder="Enter name on card" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-orange-600 hover:bg-orange-700">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Pay with Card
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Bulk Purchase for Business</CardTitle>
          <CardDescription>
            Need a larger amount of credits for your business? Contact our sales
            team for custom pricing.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Our business packages offer better rates for high-volume users. We
            can tailor a package to suit your specific needs.
          </p>
        </CardContent>
        <CardFooter>
          <Button variant="outline">Contact Sales</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
