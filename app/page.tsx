import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Shield,
  Truck,
  Clock,
  Star,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white py-12 lg:py-24">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge className="bg-orange-500/20 text-orange-400 border-orange-500/30 px-4 py-2">
                  🚀 Kenya's Leading eTIMS Solutions Provider
                </Badge>
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Transform Your Business with
                  <span className="text-orange-400"> eTIMS Solutions</span>
                </h1>
                <p className="text-xl text-gray-300 leading-relaxed">
                  Kenya's premier provider of eTIMS devices, software solutions,
                  and comprehensive business support services. Get compliant
                  with KRA requirements today.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  asChild
                  size="lg"
                  className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 text-lg"
                >
                  <Link href="/etims">
                    Explore eTIMS Solutions{" "}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-slate-900 px-8 py-4 text-lg bg-transparent"
                >
                  <Link href="/registration">Register Now</Link>
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-orange-500/20 to-orange-500/80 rounded-2xl p-4">
                <Image
                  src="/images/etims-banner-min.png"
                  alt="eTIMS System Illustration"
                  width={500}
                  height={400}
                  className="w-full h-auto rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-r from-orange-500 to-orange-600">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-orange-100">Businesses Served</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">100%</div>
              <div className="text-orange-100">KRA Compliant</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">24/7</div>
              <div className="text-orange-100">Support Available</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">5★</div>
              <div className="text-orange-100">Customer Rating</div>
            </div>
          </div>
        </div>
      </section>

      {/* eTIMS Solutions Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="bg-orange-100 text-orange-800 px-4 py-2 mb-4">
              eTIMS Solutions
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Complete eTIMS Ecosystem
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From devices to software, training to support - we provide
              everything you need for seamless eTIMS compliance
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {[
              {
                title: "eTIMS Devices",
                description:
                  "KRA-approved devices including POS systems, thermal printers, and mobile solutions",
                icon: "📱",
                features: [
                  "POS Android Terminals",
                  "Thermal Receipt Printers",
                  "Bluetooth Connectivity",
                  "Mobile Solutions",
                ],
              },
              {
                title: "Software Solutions",
                description:
                  "Comprehensive eTIMS software for seamless integration and compliance",
                icon: "💻",
                features: [
                  "Cloud-based Platform",
                  "Real-time Sync",
                  "Multi-device Support",
                  "Automated Updates",
                ],
              },
              {
                title: "Training & Support",
                description:
                  "Expert training and 24/7 technical support for your business needs",
                icon: "🎓",
                features: [
                  "On-site Training",
                  "24/7 Support",
                  "Video Tutorials",
                  "Documentation",
                ],
              },
              {
                title: "Installation",
                description:
                  "Professional installation and configuration services nationwide",
                icon: "🔧",
                features: [
                  "Professional Setup",
                  "Configuration",
                  "Testing",
                  "Go-live Support",
                ],
              },
            ].map((service, index) => (
              <Card
                key={index}
                className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <CardHeader className="text-center pb-4">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <CardTitle className="text-xl">{service.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {service.features.map((feature, idx) => (
                      <li
                        key={idx}
                        className="flex items-center text-sm text-gray-600"
                      >
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button
              asChild
              size="lg"
              className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4"
            >
              <Link href="/etims">
                View All eTIMS Solutions <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Featured eTIMS Products
            </h2>
            <p className="text-xl text-gray-600">
              Top-rated devices trusted by businesses across Kenya
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                name: "P10S Android POS Terminal",
                price: "KSh 45,000",
                image: "/placeholder.svg?height=200&width=200",
                rating: 5,
                features: [
                  "Android OS",
                  "Thermal Printer",
                  "4G Connectivity",
                  "Long Battery Life",
                ],
              },
              {
                name: "RP-F10 Bluetooth Thermal Printer",
                price: "KSh 18,500",
                image: "/placeholder.svg?height=200&width=200",
                rating: 5,
                features: [
                  "Bluetooth 4.0",
                  "58mm Paper",
                  "Fast Printing",
                  "Compact Design",
                ],
              },
              {
                name: "SmartPOS C330 Android POS",
                price: "KSh 52,000",
                image: "/placeholder.svg?height=200&width=200",
                rating: 5,
                features: [
                  "Dual Screen",
                  "NFC Payment",
                  "WiFi & 4G",
                  "Built-in Printer",
                ],
              },
              {
                name: "RP-58 Bluetooth Thermal Printer",
                price: "KSh 15,800",
                image: "/placeholder.svg?height=200&width=200",
                rating: 4,
                features: [
                  "Portable Design",
                  "Long Battery",
                  "Easy Setup",
                  "iOS & Android",
                ],
              },
            ].map((product, index) => (
              <Card
                key={index}
                className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg"
              >
                <CardHeader className="p-0">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={200}
                      height={200}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <Badge className="absolute top-4 left-4 bg-orange-500 text-white">
                      Best Seller
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="flex items-center mb-2">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < product.rating
                            ? "text-yellow-400 fill-current"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                    <span className="text-sm text-gray-600 ml-2">
                      ({product.rating}.0)
                    </span>
                  </div>
                  <h3 className="font-semibold text-lg mb-2 group-hover:text-orange-600 transition-colors">
                    {product.name}
                  </h3>
                  <div className="text-2xl font-bold text-orange-600 mb-4">
                    {product.price}
                  </div>
                  <ul className="space-y-1 mb-4">
                    {product.features.map((feature, idx) => (
                      <li
                        key={idx}
                        className="text-sm text-gray-600 flex items-center"
                      >
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                    Add to Cart
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose Tactical Business Support?
            </h2>
            <p className="text-xl text-gray-600">
              Your trusted partner for eTIMS compliance and business growth
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Shield className="h-12 w-12 text-orange-500" />,
                title: "KRA Certified",
                description:
                  "All our solutions are officially certified by Kenya Revenue Authority for full compliance.",
              },
              {
                icon: <Truck className="h-12 w-12 text-orange-500" />,
                title: "Nationwide Delivery",
                description:
                  "Fast and secure delivery across Kenya with professional installation services.",
              },
              {
                icon: <Clock className="h-12 w-12 text-orange-500" />,
                title: "24/7 Support",
                description:
                  "Round-the-clock technical support to ensure your business never stops running.",
              },
            ].map((benefit, index) => (
              <Card
                key={index}
                className="text-center border-0 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <CardHeader>
                  <div className="flex justify-center mb-4">{benefit.icon}</div>
                  <CardTitle className="text-xl">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-base">
                    {benefit.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">
            Ready to Get Started with eTIMS?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join hundreds of businesses already using our eTIMS solutions
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              size="lg"
              variant="secondary"
              className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4"
            >
              <Link href="/registration">Register Your Business</Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4 bg-transparent"
            >
              <Link href="/contact">Contact Our Experts</Link>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
