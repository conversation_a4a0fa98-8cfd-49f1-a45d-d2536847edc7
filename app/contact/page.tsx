import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  CheckCircle,
  Star,
  ArrowRight,
  Shield,
  Clock,
  Users,
  Zap,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";

export default function EtimsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-500 via-orange-600 to-red-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge className="bg-white/20 text-white px-4 py-2">
                  KRA Certified Solutions
                </Badge>
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Complete eTIMS Solutions for Your Business
                </h1>
                <p className="text-xl text-orange-100 leading-relaxed">
                  Get KRA compliant with our comprehensive eTIMS devices,
                  software, and support services. Trusted by 500+ businesses
                  across Kenya.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  asChild
                  size="lg"
                  variant="secondary"
                  className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4"
                >
                  <Link href="/registration">
                    Register Now <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4 bg-transparent"
                >
                  <Link href="#products">View Products</Link>
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h3 className="text-2xl font-bold mb-6 text-center">
                  Why Choose Our eTIMS?
                </h3>
                <div className="space-y-4">
                  {[
                    "100% KRA Certified & Compliant",
                    "24/7 Technical Support",
                    "Free Installation & Training",
                    "Cloud-based Management",
                    "Real-time Data Sync",
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-300" />
                      <span>{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What is eTIMS Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <Badge className="bg-orange-100 text-orange-800 px-4 py-2">
                  About eTIMS
                </Badge>
                <h2 className="text-4xl font-bold text-gray-900">
                  What is eTIMS?
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  eTIMS (Electronic Tax Invoice Management System) is a
                  technological advancement by the Kenya Revenue Authority to
                  streamline tax compliance. It enables real-time transmission
                  of sales data directly to KRA, ensuring transparency and
                  reducing tax evasion.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-6 w-6 text-orange-500" />
                    <h4 className="font-semibold">Compliance</h4>
                  </div>
                  <p className="text-gray-600">
                    Ensures full compliance with KRA tax regulations and
                    requirements.
                  </p>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-6 w-6 text-orange-500" />
                    <h4 className="font-semibold">Real-time</h4>
                  </div>
                  <p className="text-gray-600">
                    Instant data transmission to KRA for immediate tax
                    processing.
                  </p>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-8">
                <Image
                  src="/placeholder.svg?height=400&width=500"
                  alt="eTIMS System Illustration"
                  width={500}
                  height={400}
                  className="w-full h-auto rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Benefits of Adopting eTIMS
            </h2>
            <p className="text-xl text-gray-600">
              Transform your business operations with our comprehensive eTIMS
              solutions
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Shield className="h-12 w-12 text-orange-500" />,
                title: "Tax Compliance",
                description:
                  "Ensure full compliance with KRA regulations and avoid penalties through automated tax reporting.",
              },
              {
                icon: <Clock className="h-12 w-12 text-orange-500" />,
                title: "Time Efficiency",
                description:
                  "Reduce manual paperwork and streamline your invoicing process with automated systems.",
              },
              {
                icon: <Users className="h-12 w-12 text-orange-500" />,
                title: "Customer Trust",
                description:
                  "Build customer confidence with transparent, KRA-compliant invoicing and receipts.",
              },
              {
                icon: <Zap className="h-12 w-12 text-orange-500" />,
                title: "Real-time Reporting",
                description:
                  "Get instant insights into your business performance with real-time sales data.",
              },
              {
                icon: <CheckCircle className="h-12 w-12 text-orange-500" />,
                title: "Audit Ready",
                description:
                  "Maintain comprehensive digital records that are always ready for KRA audits.",
              },
              {
                icon: <ArrowRight className="h-12 w-12 text-orange-500" />,
                title: "Business Growth",
                description:
                  "Focus on growing your business while we handle your tax compliance requirements.",
              },
            ].map((benefit, index) => (
              <Card
                key={index}
                className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <CardHeader className="text-center">
                  <div className="flex justify-center mb-4">{benefit.icon}</div>
                  <CardTitle className="text-xl">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-center">
                    {benefit.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section id="products" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Our eTIMS Product Range
            </h2>
            <p className="text-xl text-gray-600">
              Choose from our comprehensive range of KRA-certified eTIMS devices
            </p>
          </div>

          <Tabs defaultValue="pos-terminals" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-12">
              <TabsTrigger value="pos-terminals">POS Terminals</TabsTrigger>
              <TabsTrigger value="printers">Thermal Printers</TabsTrigger>
              <TabsTrigger value="mobile">Mobile Solutions</TabsTrigger>
              <TabsTrigger value="accessories">Accessories</TabsTrigger>
            </TabsList>

            <TabsContent value="pos-terminals" className="space-y-8">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    name: "P10S Android POS Terminal",
                    price: "KSh 45,000",
                    image: "/placeholder.svg?height=250&width=250",
                    rating: 5,
                    features: [
                      "Android 7.0 OS",
                      "Built-in Thermal Printer",
                      "4G/WiFi Connectivity",
                      "8-hour Battery Life",
                    ],
                    badge: "Best Seller",
                  },
                  {
                    name: "SmartPOS C330 Android",
                    price: "KSh 52,000",
                    image: "/placeholder.svg?height=250&width=250",
                    rating: 5,
                    features: [
                      "Dual Screen Display",
                      "NFC Payment Support",
                      "Built-in Camera",
                      "Cloud Management",
                    ],
                    badge: "Premium",
                  },
                  {
                    name: "P2 Pro Android Terminal",
                    price: "KSh 38,500",
                    image: "/placeholder.svg?height=250&width=250",
                    rating: 4,
                    features: [
                      "Compact Design",
                      "Fast Processing",
                      "Multiple Connectivity",
                      "Easy Setup",
                    ],
                    badge: "Budget Friendly",
                  },
                ].map((product, index) => (
                  <Card
                    key={index}
                    className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg"
                  >
                    <CardHeader className="p-0">
                      <div className="relative overflow-hidden rounded-t-lg">
                        <Image
                          src={product.image || "/placeholder.svg"}
                          alt={product.name}
                          width={250}
                          height={250}
                          className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <Badge className="absolute top-4 left-4 bg-orange-500 text-white">
                          {product.badge}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6">
                      <div className="flex items-center mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < product.rating
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                        <span className="text-sm text-gray-600 ml-2">
                          ({product.rating}.0)
                        </span>
                      </div>
                      <h3 className="font-semibold text-lg mb-2 group-hover:text-orange-600 transition-colors">
                        {product.name}
                      </h3>
                      <div className="text-2xl font-bold text-orange-600 mb-4">
                        {product.price}
                      </div>
                      <ul className="space-y-1 mb-6">
                        {product.features.map((feature, idx) => (
                          <li
                            key={idx}
                            className="text-sm text-gray-600 flex items-center"
                          >
                            <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                      <div className="space-y-2">
                        <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                          Add to Cart
                        </Button>
                        <Button
                          variant="outline"
                          className="w-full bg-transparent"
                        >
                          View Details
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="printers" className="space-y-8">
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    name: "RP-F10 Bluetooth Thermal Printer",
                    price: "KSh 18,500",
                    image: "/placeholder.svg?height=250&width=250",
                    rating: 5,
                    features: [
                      "Bluetooth 4.0",
                      "58mm Paper Width",
                      "Fast Printing Speed",
                      "Portable Design",
                    ],
                  },
                  {
                    name: "RP-58 Bluetooth Printer",
                    price: "KSh 15,800",
                    image: "/placeholder.svg?height=250&width=250",
                    rating: 4,
                    features: [
                      "Long Battery Life",
                      "iOS & Android Support",
                      "Easy Paper Loading",
                      "Compact Size",
                    ],
                  },
                ].map((product, index) => (
                  <Card
                    key={index}
                    className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg"
                  >
                    <CardHeader className="p-0">
                      <div className="relative overflow-hidden rounded-t-lg">
                        <Image
                          src={product.image || "/placeholder.svg"}
                          alt={product.name}
                          width={250}
                          height={250}
                          className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="p-6">
                      <div className="flex items-center mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < product.rating
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                        <span className="text-sm text-gray-600 ml-2">
                          ({product.rating}.0)
                        </span>
                      </div>
                      <h3 className="font-semibold text-lg mb-2">
                        {product.name}
                      </h3>
                      <div className="text-2xl font-bold text-orange-600 mb-4">
                        {product.price}
                      </div>
                      <ul className="space-y-1 mb-6">
                        {product.features.map((feature, idx) => (
                          <li
                            key={idx}
                            className="text-sm text-gray-600 flex items-center"
                          >
                            <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                      <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                        Add to Cart
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="mobile">
              <div className="text-center py-12">
                <h3 className="text-2xl font-bold mb-4">
                  Mobile eTIMS Solutions
                </h3>
                <p className="text-gray-600 mb-8">
                  Perfect for businesses on the go
                </p>
                <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                  Coming Soon
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="accessories">
              <div className="text-center py-12">
                <h3 className="text-2xl font-bold mb-4">eTIMS Accessories</h3>
                <p className="text-gray-600 mb-8">
                  Enhance your eTIMS setup with our accessories
                </p>
                <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                  View Accessories
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Get answers to common questions about eTIMS
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              <AccordionItem
                value="item-1"
                className="bg-white rounded-lg border-0 shadow-sm"
              >
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  Do you offer eTIMS machine sales service?
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4">
                  Yes, we offer comprehensive eTIMS machine sales with full KRA
                  certification. All our devices come with warranty,
                  installation support, and training.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem
                value="item-2"
                className="bg-white rounded-lg border-0 shadow-sm"
              >
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  Do you offer training on how to use the KRA eTIMS devices?
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4">
                  We provide comprehensive training programs including on-site
                  training, video tutorials, and ongoing support to ensure you
                  can use your eTIMS devices effectively.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem
                value="item-3"
                className="bg-white rounded-lg border-0 shadow-sm"
              >
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  Do you offer installation, setup or configure the devices?
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4">
                  Yes, we offer complete installation and configuration
                  services. Our certified technicians will set up your devices,
                  configure them according to KRA requirements, and ensure
                  everything is working perfectly before handover.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem
                value="item-4"
                className="bg-white rounded-lg border-0 shadow-sm"
              >
                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                  What support do you provide after installation?
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4">
                  We provide 24/7 technical support, regular software updates,
                  maintenance services, and troubleshooting assistance. Our
                  support team is always ready to help you maintain compliance
                  and resolve any issues.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">
            Ready to Get Started with eTIMS?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join hundreds of businesses already using our eTIMS solutions
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              size="lg"
              variant="secondary"
              className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4"
            >
              <Link href="/registration">
                Register Your Business <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4 bg-transparent"
            >
              <Link href="/contact">Contact Our Experts</Link>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
