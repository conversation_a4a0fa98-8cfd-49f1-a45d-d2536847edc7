import { apiGet, apiPost } from "@/lib/auth/api-client";
import { TaxCategory } from "@/types";

// Tax Categories service
export const taxCategoryService = {
  createTaxCategory: (
    data: { description: string; store_id: number },
    token: string | null
  ) => {
    return apiPost<TaxCategory>("/tax_categories", data, token);
  },

  filterTaxCategories: (token: string | null) => {
    return apiGet<{
      tax_categories: TaxCategory[];
      pagination: { count: number; page: number; per: number };
    }>("/tax_categories", token);
  },

  getTaxCategoryById: (id: number, token: string | null) => {
    return apiGet(`/tax_categories/${id}`, token);
  },

  searchTaxCategories: (token: string, searchTerm: string | null) => {
    return apiGet<{
      tax_categories: TaxCategory[];
      pagination: { count: number; page: number; per: number };
    }>(`/tax_categories?search=${searchTerm}`, token);
  },
};
