export const sharedFunctions = {
  capitalize: (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  },

  // Create function to format amount
  formatAmount: (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency,
    }).format(amount);
  },

  // Format number adding commas
  formatNumber: (number: number) => {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  },
};
