import { apiGet, apiPost, apiPut } from '@/lib/auth/api-client';
import { Attachment, DashboardAnalytics, UserIncome } from '@/types';

// UserIncome service
export const userIncomeService = {
  createUserIncome: (
    data: {
      attachment: Attachment;
      tax_category_id: number;
      income_date: string;
      merchant_name: string;
      merchant_pin: string;
      amount: number;
      currency: string;
      description: string;
      payment_frequency: string;
      start_date: string;
      end_date: string;
      withholding_tax_rate: number;
      withholding_tax_amount: number;
    },
    token: string | null
  ) => {
    return apiPost<UserIncome>('/user_incomes', data, token);
  },

  filterUserIncomes: (token: string | null) => {
    return apiGet<{
      user_incomes: UserIncome[];
      pagination: { count: number; page: number; per: number };
    }>('/user_incomes', token);
  },

  getIncomeDashboardAnalytics: (token: string | null) => {
    return apiGet<DashboardAnalytics>('/analytics/incomes', token);
  },

  getUserIncomeById: (id: number, token: string | null) => {
    return apiGet<UserIncome>(`/user_incomes/${id}`, token);
  },

  searchUserIncomes: (token: string | null, keyword: string) => {
    return apiGet<{
      user_incomes: UserIncome[];
      pagination: { count: number; page: number; per: number };
    }>(`/user_incomes?search=${keyword}`, token);
  },

  updateUserIncome: (
    id: number,
    data: {
      amount: number;
      currency: string;
      description: string;
      tax_category_id: number;
      income_date: string;
      merchant_name: string;
      merchant_pin: string;
      payment_frequency: string;
      start_date: string;
      end_date: string;
      withholding_tax_rate: number;
      withholding_tax_amount: number;
    },
    token: string | null
  ) => {
    return apiPut<UserIncome>(`/user_incomes/${id}`, data, token);
  },
};
