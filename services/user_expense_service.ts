import { apiGet, apiPost, apiPut } from '@/lib/auth/api-client';
import { Attachment, DashboardAnalytics, UserExpense } from '@/types';

// UserExpense service
export const userExpenseService = {
  createUserExpense: (
    data: {
      attachment: Attachment;
      amount: number;
      currency: string;
      description: string;
      expense_date: string;
      expense_type_id: number;
      merchant_name: string;
      merchant_pin: string;
    },
    token: string | null
  ) => {
    return apiPost<UserExpense>('/user_expenses', data, token);
  },

  filterUserExpenses: (token: string | null) => {
    return apiGet<{
      user_expenses: UserExpense[];
      pagination: { count: number; page: number; per: number };
    }>('/user_expenses', token);
  },

  getExpenseDashboardAnalytics: (token: string | null) => {
    return apiGet<DashboardAnalytics>('/analytics/expenses', token);
  },

  getUserExpenseById: (id: number, token: string | null) => {
    return apiGet<UserExpense>(`/user_expenses/${id}`, token);
  },

  searchUserExpenses: (token: string | null, keyword: string) => {
    return apiGet<{
      user_expenses: UserExpense[];
      pagination: { count: number; page: number; per: number };
    }>(`/user_expenses?search=${keyword}`, token);
  },

  updateUserExpense: (
    id: number,
    data: {
      amount: number;
      currency: string;
      description: string;
      expense_date: string;
      expense_type_id: number;
      merchant_name: string;
      merchant_pin: string;
    },
    token: string | null
  ) => {
    return apiPut<UserExpense>(`/user_expenses/${id}`, data, token);
  },
};
