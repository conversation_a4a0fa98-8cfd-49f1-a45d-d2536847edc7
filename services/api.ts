const API_BASE_URL = "https://tacticalapi.growthlogic.co.ke/api";

import { apiGet, apiPost } from "@/lib/auth/api-client";
import { Api<PERSON><PERSON>, DashboardAnalytics } from "@/types";

interface Attachment {
  async: boolean;
  content_md5: string;
  content_size: number;
  content_type: string;
  duration: number;
  data: string;
  name: string;
}

interface ClientRegistrationPayload {
  business_name: string;
  business_pin: string;
  company_address: string;
  company_email: string;
  company_phone_number: string;
  director_address: string;
  director_email: string;
  director_id_front: Attachment;
  director_id_back: Attachment;
  director_name: string;
  director_phone_number: string;
  director_pin: string;
  form_filled_by: string;
  trader_invoicing_system: string;
}

export const registerClient = async (
  payload: ClientRegistrationPayload
): Promise<any> => {
  // Convert payload to JSON format, keeping attachments as objects
  const jsonPayload = { ...payload };

  console.log("Sending JSON payload:", jsonPayload);

  try {
    const response = await fetch(`${API_BASE_URL}/clients`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(jsonPayload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
};

export const checkHealth = async (): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Health check failed:", error);
    throw error;
  }
};

// Analytics service
export const analyticsService = {
  getDashboardAnalytics: (token: string | null) => {
    return apiGet<DashboardAnalytics>("/analytics", token);
  },
};

// API Keys service
export const apiKeysService = {
  createApiKey: (
    data: { description: string; store_id: number },
    token: string | null
  ) => {
    return apiPost<ApiKey>("/api_keys", data, token);
  },

  getApiKeys: (token: string | null) => {
    return apiGet<{
      api_keys: ApiKey[];
      pagination: { count: number; page: number; per: number };
    }>("/api_keys", token);
  },

  getApiKeyById: (id: number, token: string | null) => {
    return apiGet(`/api_keys/${id}`, token);
  },
};

// Health check service
export const healthService = {
  checkHealth: () => {
    return apiGet<{ message: string; status: string }>("/health", null);
  },
};
