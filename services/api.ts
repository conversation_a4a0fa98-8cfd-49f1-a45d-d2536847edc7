const API_BASE_URL = "https://tacticalapi.growthlogic.co.ke/api";

interface Attachment {
  async: boolean;
  content_md5: string;
  content_size: number;
  content_type: string;
  duration: number;
  data: string;
  name: string;
}

interface ClientRegistrationPayload {
  business_name: string;
  business_pin: string;
  company_address: string;
  company_email: string;
  company_phone_number: string;
  director_address: string;
  director_email: string;
  director_id_front: Attachment;
  director_id_back: Attachment;
  director_name: string;
  director_phone_number: string;
  director_pin: string;
  form_filled_by: string;
  trader_invoicing_system: string;
}

export const registerClient = async (
  payload: ClientRegistrationPayload
): Promise<any> => {
  const formData = new FormData();

  for (const key in payload) {
    if (payload.hasOwnProperty(key)) {
      const value = payload[key as keyof ClientRegistrationPayload];
      if (value !== null) {
        if (typeof value === "object" && "data" in value) {
          // Handle Attachment objects
          const attachment = value as Attachment;
          // Convert base64 data to Blob
          const byteCharacters = atob(attachment.data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: attachment.content_type });
          formData.append(key, blob);

          // Append attachment metadata
          console.log(
            `Appending content_size for ${key}:`,
            attachment.content_size
          );

          // Try multiple naming conventions in case server expects different format
          formData.append(
            `${key}_content_size`,
            attachment.content_size.toString()
          );
          formData.append(
            `${key}[content_size]`,
            attachment.content_size.toString()
          );
          formData.append(`content_size`, attachment.content_size.toString());

          formData.append(`${key}_content_type`, attachment.content_type);
          formData.append(`${key}_content_md5`, attachment.content_md5);
          formData.append(`${key}_async`, attachment.async.toString());
        } else {
          // Handle string, number, and other primitive values
          formData.append(key, String(value));
        }
      }
    }
  }

  // Debug: Log all FormData entries
  console.log("FormData entries:");
  for (const [key, value] of formData.entries()) {
    console.log(`${key}:`, value);
  }

  try {
    const response = await fetch(`${API_BASE_URL}/clients`, {
      method: "POST",
      body: formData,
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
};

export const checkHealth = async (): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Health check failed:", error);
    throw error;
  }
};
