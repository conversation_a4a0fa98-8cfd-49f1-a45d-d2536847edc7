import { apiGet, apiPost, apiPut } from "@/lib/auth/api-client";
import { Attachment, DashboardAnalytics, Client } from "@/types";

// client service
export const clientService = {
  createClient: (
    data: {
      attachment: Attachment;
      tax_category_id: number;
      income_date: string;
      merchant_name: string;
      merchant_pin: string;
      amount: number;
      currency: string;
      description: string;
      payment_frequency: string;
      start_date: string;
      end_date: string;
      withholding_tax_rate: number;
      withholding_tax_amount: number;
    },
    token: string | null
  ) => {
    return apiPost<Client>("/clients", data, token);
  },

  filterClients: (token: string | null) => {
    return apiGet<{
      clients: Client[];
      pagination: { count: number; page: number; per: number };
    }>("/clients", token);
  },

  getIncomeDashboardAnalytics: (token: string | null) => {
    return apiGet<DashboardAnalytics>("/analytics/incomes", token);
  },

  getClientById: (id: number, token: string | null) => {
    return apiGet<Client>(`/clients/${id}`, token);
  },

  searchClients: (token: string | null, keyword: string) => {
    return apiGet<{
      clients: Client[];
      pagination: { count: number; page: number; per: number };
    }>(`/clients?search=${keyword}`, token);
  },

  updateClient: (
    id: number,
    data: {
      amount: number;
      currency: string;
      description: string;
      tax_category_id: number;
      income_date: string;
      business_name: string;
      merchant_pin: string;
      payment_frequency: string;
      start_date: string;
      end_date: string;
      withholding_tax_rate: number;
      withholding_tax_amount: number;
    },
    token: string | null
  ) => {
    return apiPut<Client>(`/clients/${id}`, data, token);
  },
};
