import MD5 from "blueimp-md5";

// fuction to get the file's data as a base64 encoded string
function getBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const result = reader.result;
      if (typeof result === "string") {
        resolve(result);
      } else {
        reject(new Error("File could not be read as base64 string."));
      }
    };

    reader.onerror = (error) => reject(error);

    reader.readAsDataURL(file);
  });
}

export const processImage = async (originalFile: File) => {
  // get the image's data as a base64 encoded string
  const dataUrl = await getBase64(originalFile)
    .then((result) => result)
    .catch((error) => {
      /**Failed to read image */
      throw error;
    });

  // set the asset payload
  const filePayload = {
    async: false,
    content_md5: MD5(dataUrl).toString(),
    content_size: originalFile.size, // compressedImage.size
    content_type: originalFile.type, // compressedImage.type
    duration: null,
    data: dataUrl.split(",")[1],
    name: originalFile.name,
    // uid: originalFile.uid,
  };

  return filePayload;
};
