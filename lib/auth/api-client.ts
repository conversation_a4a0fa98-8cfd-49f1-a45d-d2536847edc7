const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL ||
  "https://tacticalapi.growthlogic.co.ke/api";

interface RequestOptions extends RequestInit {
  token?: string | null;
}

export async function apiClient<T>(
  endpoint: string,
  { token, ...customConfig }: RequestOptions = {}
): Promise<T> {
  const headers = new Headers({
    "Content-Type": "application/json",
    ...(token ? { "x-tactical-token": token } : {}),
    ...customConfig.headers,
  });

  const config = {
    ...customConfig,
    headers,
  };

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

    if (!response.ok) {
      // If the response is 401 Unauthorized, we should logout the user
      if (response.status === 401) {
        // Clear local storage
        localStorage.removeItem("auth_token");
        localStorage.removeItem("auth_user");

        // Redirect to login page
        window.location.href = "/login";
      }

      const errorData = await response.json().catch(() => ({}));
      return Promise.reject(
        new Error(errorData.message || response.statusText)
      );
    }

    // For empty responses
    if (response.status === 204) {
      return {} as T;
    }

    // Parse JSON response
    const data = await response.json();
    return data;
  } catch (error) {
    return Promise.reject(error);
  }
}

// Helper methods for common HTTP methods
export const apiGet = <T>(
  endpoint: string,
  token?: string | null
): Promise<T> => apiClient<T>(endpoint, { token });

export const apiPost = <T>(
  endpoint: string,
  data: any,
  token?: string | null
): Promise<T> =>
  apiClient<T>(endpoint, { method: "POST", body: JSON.stringify(data), token });

export const apiPut = <T>(
  endpoint: string,
  data: any,
  token?: string | null
): Promise<T> =>
  apiClient<T>(endpoint, { method: "PUT", body: JSON.stringify(data), token });

export const apiDelete = <T>(
  endpoint: string,
  token?: string | null
): Promise<T> => apiClient<T>(endpoint, { method: "DELETE", token });
