"use client";

import type React from "react";

import { createContext, useContext, useEffect, useState } from "react";
import { jwtDecode } from "jwt-decode";
import { useRouter } from "next/navigation";
import { WholeUser, JwtPayload } from "@/types";

export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: {
    country_code: string;
    number: string;
  };
}

interface AuthContextType {
  user: WholeUser | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  signup: (
    userData: SignupData
  ) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
}

export interface SignupData {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: {
    country_code: string;
    number: string;
  };
  residence: "kenyan" | "non-kenyan";
  password: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL ||
  "https://tacticalapi.growthlogic.co.ke/api";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<WholeUser | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Check for existing token on mount
  useEffect(() => {
    const storedToken = localStorage.getItem("auth_token");
    const storedUser = localStorage.getItem("auth_user");

    if (storedToken && storedUser) {
      setToken(storedToken);
      setUser(JSON.parse(storedUser));
    }

    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/sessions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-tactical-application": "user",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || "Login failed. Please check your credentials.",
        };
      }

      // Get token from headers
      const authToken = response.headers.get("x-tactical-token");

      if (!authToken) {
        return {
          success: false,
          error: "Authentication token not found in response.",
        };
      }

      // Save token and user data
      localStorage.setItem("auth_token", authToken);
      localStorage.setItem("auth_user", JSON.stringify(data.user));

      setToken(authToken);
      setUser(data.user);

      return { success: true };
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        error: "An unexpected error occurred. Please try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (userData: SignupData) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/users`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || "Registration failed. Please try again.",
        };
      }

      return { success: true };
    } catch (error) {
      console.error("Signup error:", error);
      return {
        success: false,
        error: "An unexpected error occurred. Please try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);

      if (token) {
        const currentSession = jwtDecode<JwtPayload>(token);
        // Make API call to logout
        await fetch(`${API_BASE_URL}/sessions/${currentSession.session_id}`, {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            "x-tactical-token": token,
          },
        });
      }

      // Clear local storage and state
      localStorage.removeItem("auth_token");
      localStorage.removeItem("auth_user");
      setToken(null);
      setUser(null);

      // Redirect to login page
      router.push("/login");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    token,
    isLoading,
    isAuthenticated: !!token,
    login,
    signup,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
