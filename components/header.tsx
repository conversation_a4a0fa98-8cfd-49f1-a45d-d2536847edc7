"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Search, Menu, ShoppingCart, User, Phone, Heart } from "lucide-react"
import Link from "next/link"

export function Header() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <header className="border-b bg-white sticky top-0 z-50 shadow-sm">
      {/* Top Bar */}
      <div className="bg-slate-900 text-white py-2 hidden">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <Phone className="h-4 w-4 mr-1" />
                +254 700 000 000
              </span>
              <span>Free shipping on orders over KSh 10,000</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/account" className="hover:text-orange-400 transition-colors">
                My Account
              </Link>
              <Link href="/track" className="hover:text-orange-400 transition-colors">
                Track Order
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="bg-orange-500 text-white px-3 py-2 rounded-lg font-bold text-xl">TACTICAL</div>
            <div className="hidden sm:block">
              <div className="text-sm font-semibold text-gray-900">Business Support Ltd</div>
              <div className="text-xs text-gray-600">Technology • Support • Business</div>
            </div>
          </Link>

          {/* Search Bar */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <div className="relative w-full">
              <Input
                type="search"
                placeholder="Search for eTIMS devices, printers, and more..."
                className="pl-4 pr-12 py-3 w-full border-2 border-gray-200 focus:border-orange-500"
              />
              <Button size="sm" className="absolute right-1 top-1 bg-orange-500 hover:bg-orange-600 text-white px-4">
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="hidden md:flex items-center">
              <Heart className="h-5 w-5 mr-1" />
              <span className="hidden lg:inline">Wishlist</span>
            </Button>
            <Button variant="ghost" size="sm" className="flex items-center">
              <ShoppingCart className="h-5 w-5 mr-1" />
              <span className="hidden lg:inline">Cart</span>
              <Badge className="ml-1 bg-orange-500 text-white">0</Badge>
            </Button>
            <Button variant="ghost" size="sm" className="hidden md:flex items-center">
              <User className="h-5 w-5 mr-1" />
              <span className="hidden lg:inline">Account</span>
            </Button>

            {/* Mobile Menu */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="md:hidden">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <div className="space-y-6 mt-6">
                  <div className="space-y-4">
                    <Link href="/etims" className="block text-lg font-semibold hover:text-orange-600">
                      eTIMS Solutions
                    </Link>
                    <Link href="/products" className="block text-lg font-semibold hover:text-orange-600">
                      Products
                    </Link>
                    <Link href="/services" className="block text-lg font-semibold hover:text-orange-600">
                      Services
                    </Link>
                    <Link href="/support" className="block text-lg font-semibold hover:text-orange-600">
                      Support
                    </Link>
                    <Link href="/contact" className="block text-lg font-semibold hover:text-orange-600">
                      Contact
                    </Link>
                  </div>
                  <div className="pt-6 border-t">
                    <Button asChild className="w-full bg-orange-500 hover:bg-orange-600 text-white mb-3">
                      <Link href="/registration">Register for eTIMS</Link>
                    </Button>
                    <div className="space-y-2">
                      <Input placeholder="Search products..." />
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="border-t bg-gray-50">
        <div className="container mx-auto px-4">
          <NavigationMenu className="hidden md:flex">
            <NavigationMenuList className="space-x-8">
              <NavigationMenuItem>
                <NavigationMenuTrigger className="bg-transparent hover:bg-orange-50 hover:text-orange-600 data-[state=open]:bg-orange-50">
                  eTIMS Solutions
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div className="grid w-[600px] grid-cols-2 p-4">
                    <div className="space-y-3">
                      <h4 className="font-semibold text-orange-600">Devices</h4>
                      <NavigationMenuLink asChild>
                        <Link href="/etims/pos-terminals" className="block p-2 hover:bg-gray-50 rounded">
                          POS Terminals
                        </Link>
                      </NavigationMenuLink>
                      <NavigationMenuLink asChild>
                        <Link href="/etims/printers" className="block p-2 hover:bg-gray-50 rounded">
                          Thermal Printers
                        </Link>
                      </NavigationMenuLink>
                      <NavigationMenuLink asChild>
                        <Link href="/etims/mobile" className="block p-2 hover:bg-gray-50 rounded">
                          Mobile Solutions
                        </Link>
                      </NavigationMenuLink>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-orange-600">Services</h4>
                      <NavigationMenuLink asChild>
                        <Link href="/etims/software" className="block p-2 hover:bg-gray-50 rounded">
                          eTIMS Software
                        </Link>
                      </NavigationMenuLink>
                      <NavigationMenuLink asChild>
                        <Link href="/etims/training" className="block p-2 hover:bg-gray-50 rounded">
                          Training & Support
                        </Link>
                      </NavigationMenuLink>
                      <NavigationMenuLink asChild>
                        <Link href="/etims/installation" className="block p-2 hover:bg-gray-50 rounded">
                          Installation Services
                        </Link>
                      </NavigationMenuLink>
                    </div>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link href="/products" className="px-4 py-2 hover:text-orange-600 transition-colors">
                    All Products
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link href="/services" className="px-4 py-2 hover:text-orange-600 transition-colors">
                    Services
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link href="/support" className="px-4 py-2 hover:text-orange-600 transition-colors">
                    Support
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <NavigationMenuLink asChild>
                  <Link href="/contact" className="px-4 py-2 hover:text-orange-600 transition-colors">
                    Contact
                  </Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              <NavigationMenuItem>
                <Button asChild size="sm" className="bg-orange-500 hover:bg-orange-600 text-white ml-4">
                  <Link href="/registration">Register for eTIMS</Link>
                </Button>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </div>
    </header>
  )
}
