"use client";

import type React from "react";

import { useState } from "react";
import { useAuth } from "@/context/auth-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";
// import type { TransactionDetails } from "./transaction-details-modal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { hashingService, DecodedHash } from "@/services/api";

interface SingleHashDecoderProps {
  onDecodeSuccess?: (transaction: DecodedHash) => void;
}

export function SingleHashDecoder({ onDecodeSuccess }: SingleHashDecoderProps) {
  const { token } = useAuth();
  const [hash, setHash] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    data?: DecodedHash;
  } | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!hash.trim()) return;

    setIsLoading(true);
    setResult(null);
    hashingService
      .decodeHash(hash, token)
      .then((response) => {
        // populate the result with the response data
        setResult(() => ({
          success: true,
          message: "Hash decoded successfully.",
          data: response,
        }));
        // invoke the modal
        if (onDecodeSuccess && response) {
          onDecodeSuccess(response);
        }
      })
      .catch((error) => {
        setResult(() => ({
          success: false,
          message: error?.response?.data?.message || "Failed to decode hash.",
        }));
      })
      .finally(() => {
        setIsLoading(false);
      });

    // Simulate API call
    // setTimeout(() => {
    //   setIsLoading(false);

    //   // Mock successful response
    //   const mockTransaction: TransactionDetails = {
    //     id: "TRX-" + Math.floor(Math.random() * 10000),
    //     hash: hash,
    //     date: new Date().toISOString().split("T")[0],
    //     time: new Date().toTimeString().split(" ")[0],
    //     amount: "KSh " + (Math.floor(Math.random() * 10000) + 100),
    //     phoneNumber: "+254 7" + Math.floor(Math.random() * ********),
    //     telcoProvider: "Safaricom",
    //     transactionType: "Customer Payment",
    //     status: "completed",
    //     recipientName: "John Doe Merchant",
    //     recipientAccount: "Business Account",
    //     reference: "INV-" + Math.floor(Math.random() * 1000),
    //     description: "Payment for services",
    //   };

    //   const successResult = {
    //     success: true,
    //     message: "Hash decoded successfully.",
    //     data: mockTransaction,
    //   };

    //   setResult(successResult);

    //   if (onDecodeSuccess && successResult.data) {
    //     onDecodeSuccess(successResult.data);
    //   }
    // }, 1500);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Decode Single Hash</CardTitle>
        <CardDescription>
          Enter an Mobile-money transaction hash to decode and view the
          transaction details.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Input
              placeholder="Enter Mobile-money transaction hash e.g 4166369d0fde6a45b12b4bf3fdde10ae3196eb4ecfbc231e743adf4256d97107"
              value={hash}
              onChange={(e) => setHash(e.target.value)}
              className="flex-1 font-mono"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={isLoading || !hash.trim()}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Decoding...
                </>
              ) : (
                "Decode Hash"
              )}
            </Button>
          </div>

          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
              <AlertDescription>{result.message}</AlertDescription>
            </Alert>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
