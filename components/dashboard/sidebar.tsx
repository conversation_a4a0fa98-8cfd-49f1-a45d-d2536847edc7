"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { cn } from "@/lib/utils";
import {
  Banknote,
  Coins,
  Key,
  LayoutDashboard,
  ListChecks,
  Settings,
  Wallet,
  ChevronDown,
  ChevronRight,
  TrendingUp,
  TrendingDown,
} from "lucide-react";

interface SidebarSubItem {
  title: string;
  href: string;
  icon: any;
}

interface SidebarItem {
  title: string;
  href: string;
  icon: any;
  subItems?: SidebarSubItem[];
}

// todo: disable routes to be used by admins only
export const sidebarItems: SidebarItem[] = [
  {
    title: "Dashboards",
    href: "/dashboard",
    icon: LayoutDashboard,
    subItems: [
      {
        title: "Incomes",
        href: "/dashboard/user-incomes/dashboard",
        icon: TrendingUp,
      },
      {
        title: "Expenses",
        href: "/dashboard/user-expenses/dashboard",
        icon: TrendingDown,
      },
      // Add one that represents all transactions
      {
        title: "All Transactions",
        href: "/dashboard",
        icon: Wallet,
      },
    ],
  },
  // { title: "Reports", href: "/dashboard/reports", icon: BarChart3, },
  {
    title: "Income Tracker",
    href: "/dashboard/user-incomes",
    icon: Banknote,
  },
  {
    title: "Expense Tracker",
    href: "/dashboard/user-expenses",
    icon: Wallet,
  },
  // {
  //   title: "Expense Types",
  //   href: "/dashboard/expense-types",
  //   icon: Wallet,
  // },
  // {
  //   title: "Compliance Calender",
  //   href: "/dashboard/compliance-calender",
  //   icon: Calendar,
  // },
  {
    title: "Tax Categories",
    href: "/dashboard/tax-categories",
    icon: ListChecks,
  },

  // { title: "Requests", href: "/dashboard/requests", icon: FileText, },
  {
    title: "API Keys",
    href: "/dashboard/api-keys",
    icon: Key,
  },
  {
    title: "Billing & Topup",
    href: "/dashboard/topup",
    icon: Coins,
  },
  {
    title: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
  },
];

export function DashboardSidebar({
  isOpen,
  onNavigate,
}: {
  isOpen: boolean;
  onNavigate?: () => void;
}) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (href: string) => {
    setExpandedItems((prev) =>
      prev.includes(href)
        ? prev.filter((item) => item !== href)
        : [...prev, href]
    );
  };

  const isExpanded = (href: string) => expandedItems.includes(href);

  return (
    <div
      className={cn(
        "h-screen fixed top-0 left-0 bg-background border-r z-30 transition-all duration-300 ease-in-out",
        isOpen ? "w-[260px]" : "w-0 -ml-[1px] overflow-hidden"
      )}
    >
      <div className="flex h-16 items-center border-b px-4 lg:px-6">
        <Link href="/dashboard" className="flex items-center space-x-2">
          <div className="h-6 w-6 rounded-full bg-green-600 flex items-center justify-center">
            <span className="font-bold text-white text-xs">C</span>
          </div>
          <span className="font-bold">Compliance 360</span>
        </Link>
      </div>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto py-2">
        <nav className="grid items-start px-2 text-sm font-medium">
          {sidebarItems.map((item) => (
            <div key={item.href}>
              {/* Main menu item */}
              {item.subItems ? (
                <button
                  onClick={() => toggleExpanded(item.href)}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary w-full text-left",
                    pathname === item.href ||
                      item.subItems.some((subItem) => pathname === subItem.href)
                      ? "bg-muted text-primary"
                      : "text-muted-foreground"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  {item.title}
                  {isExpanded(item.href) ? (
                    <ChevronDown className="h-4 w-4 ml-auto" />
                  ) : (
                    <ChevronRight className="h-4 w-4 ml-auto" />
                  )}
                </button>
              ) : (
                <Link
                  href={item.href}
                  onClick={onNavigate}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary",
                    pathname === item.href
                      ? "bg-muted text-primary"
                      : "text-muted-foreground"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  {item.title}
                </Link>
              )}

              {/* Sub menu items */}
              {item.subItems && isExpanded(item.href) && (
                <div className="ml-4 mt-1 space-y-1">
                  {item.subItems.map((subItem) => (
                    <Link
                      key={subItem.href}
                      href={subItem.href}
                      onClick={onNavigate}
                      className={cn(
                        "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary text-sm",
                        pathname === subItem.href
                          ? "bg-muted text-primary"
                          : "text-muted-foreground"
                      )}
                    >
                      <subItem.icon className="h-3 w-3" />
                      {subItem.title}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  );
}
