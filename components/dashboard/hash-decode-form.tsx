"use client";

import type React from "react";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";

export function HashDecodeForm() {
  const [hash, setHash] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<null | {
    success: boolean;
    message: string;
  }>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!hash.trim()) return;

    setIsLoading(true);
    setResult(null);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setResult({
        success: true,
        message:
          "Hash decoded successfully. Transaction details are available in your dashboard.",
      });
    }, 1500);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="hash">Mobile Money Transaction Hash</Label>
        <Input
          id="hash"
          placeholder="Enter transaction hash"
          value={hash}
          onChange={(e) => setHash(e.target.value)}
          className="font-mono"
        />
      </div>
      <Button
        type="submit"
        disabled={isLoading || !hash.trim()}
        className="w-full bg-green-600 hover:bg-green-700"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Decoding...
          </>
        ) : (
          "Decode Hash"
        )}
      </Button>
      {result && (
        <div
          className={`text-sm ${
            result.success ? "text-green-600" : "text-red-600"
          }`}
        >
          {result.message}
        </div>
      )}
    </form>
  );
}
