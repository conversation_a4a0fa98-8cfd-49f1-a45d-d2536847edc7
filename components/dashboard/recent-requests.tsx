import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Eye } from "lucide-react"

export function RecentRequests() {
  // Mock data for recent requests
  const recentRequests = [
    {
      id: "REQ-001",
      hash: "200020001234567890123456789012345678901234",
      date: "2023-05-20",
      status: "completed",
      source: "API",
    },
    {
      id: "REQ-002",
      hash: "200020002345678901234567890123456789012345",
      date: "2023-05-19",
      status: "completed",
      source: "Dashboard",
    },
    {
      id: "REQ-003",
      hash: "200020003456789012345678901234567890123456",
      date: "2023-05-18",
      status: "failed",
      source: "API",
    },
    {
      id: "REQ-004",
      hash: "200020004567890123456789012345678901234567",
      date: "2023-05-17",
      status: "completed",
      source: "File Upload",
    },
    {
      id: "REQ-005",
      hash: "200020005678901234567890123456789012345678",
      date: "2023-05-16",
      status: "processing",
      source: "Dashboard",
    },
  ]

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Request ID</TableHead>
            <TableHead>Hash</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Source</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {recentRequests.map((request) => (
            <TableRow key={request.id}>
              <TableCell className="font-medium">{request.id}</TableCell>
              <TableCell className="font-mono text-xs">{request.hash.substring(0, 15)}...</TableCell>
              <TableCell>{request.date}</TableCell>
              <TableCell>{request.source}</TableCell>
              <TableCell>
                <Badge
                  variant={
                    request.status === "completed"
                      ? "success"
                      : request.status === "processing"
                        ? "outline"
                        : "destructive"
                  }
                >
                  {request.status}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="icon">
                  <Eye className="h-4 w-4" />
                  <span className="sr-only">View details</span>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
