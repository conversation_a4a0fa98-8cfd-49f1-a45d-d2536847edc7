"use client";

import { useState } from "react";
import { format } from "date-fns";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Copy, Check } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { PhoneNumberHash } from "@/types";

// Define the transaction details type
export interface TransactionDetails extends PhoneNumberHash {
  amount?: string;
  transactionType?: string;
  status: string;
  recipientName?: string;
  recipientAccount?: string;
  reference?: string;
  description?: string;
}

interface TransactionDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: PhoneNumberHash | null;
}

export function TransactionDetailsModal({
  isOpen,
  onClose,
  transaction,
}: TransactionDetailsModalProps) {
  const [copiedField, setCopiedField] = useState<string | null>(null);

  if (!transaction) return null;

  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(null), 2000);
  };

  // Format the status for display
  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "success";
      case "processing":
        return "outline";
      case "failed":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="min-w-max">
        <DialogHeader>
          <DialogTitle>Transaction Details</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Transaction ID and Status */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Transaction ID
              </h3>
              <p className="font-medium">{transaction.id}</p>
            </div>
            <Badge
              variant={getStatusVariant("completed")}
              className="capitalize self-start sm:self-center"
            >
              completed
            </Badge>
          </div>

          <Separator />

          {/* Transaction Hash */}
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              Transaction Hash
            </h3>
            <div className="flex items-center gap-2">
              <code className="bg-muted px-2 py-1 rounded text-xs font-mono flex-1 overflow-x-auto">
                {transaction.hash}
              </code>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => copyToClipboard(transaction.hash, "hash")}
              >
                {copiedField === "hash" ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4 text-muted-foreground" />
                )}
                <span className="sr-only">Copy hash</span>
              </Button>
            </div>
          </div>

          {/* Transaction Details Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">
                Phone Number
              </h3>
              <div className="flex items-center gap-2">
                <p>{transaction.phone_number}</p>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() =>
                    copyToClipboard(transaction.phone_number, "phone")
                  }
                >
                  {copiedField === "phone" ? (
                    <Check className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3 text-muted-foreground" />
                  )}
                  <span className="sr-only">Copy phone number</span>
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">
                Telco Provider
              </h3>
              <p className="capitalize">{transaction.provider}</p>
            </div>

            {transaction?.created_at && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">
                  Date & Time
                </h3>
                <p>
                  {format(transaction.created_at, "dd MMM yyyy")} at{" "}
                  {format(transaction.created_at, "hh:mm a")}
                </p>
              </div>
            )}

            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">
                New Balance
              </h3>
              <p className="font-semibold">KSh {transaction?.new_balance}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button variant="outline" className="hidden">
            <Copy className="mr-2 h-4 w-4" />
            Export Details
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
