"use client";

import React, { useEffect } from "react";
import confetti from "canvas-confetti";

const Confetti = () => {
  useEffect(() => {
    const duration = 2 * 1000; // prev: 3secs
    const animationEnd = Date.now() + duration;

    (function frame() {
      confetti({
        particleCount: 20, // prev: 200
        spread: 70,
        origin: { y: 0.6 },
      });

      if (Date.now() < animationEnd) {
        requestAnimationFrame(frame);
      }
    })();
  }, []);

  return null; // This component doesn't render anything visible directly
};

export default Confetti;
