"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useCookieConsent } from "./cookie-context";

export function CookieBanner() {
  const { showBanner, acceptAll, declineAll, openSettings } =
    useCookieConsent();

  if (!showBanner) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 md:p-6 bg-background border-t shadow-lg">
      <div className="container max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
          <div className="space-y-2 flex-1">
            <h3 className="text-lg font-semibold"><PERSON><PERSON></h3>
            <p className="text-sm text-muted-foreground">
              We use cookies to enhance your browsing experience, serve
              personalized ads or content, and analyze our traffic. By clicking
              "Accept All", you consent to our use of cookies. Visit our{" "}
              <a href="/privacy" className="text-green-600 hover:underline">
                Privacy Policy
              </a>{" "}
              to learn more.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:items-center">
            <Button variant="outline" size="sm" onClick={declineAll}>
              Decline All
            </Button>
            <Button variant="outline" size="sm" onClick={openSettings}>
              Cookie Settings
            </Button>
            <Button
              size="sm"
              className="bg-orange-600 hover:bg-orange-700"
              onClick={acceptAll}
            >
              Accept All
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
