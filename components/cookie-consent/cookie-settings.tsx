"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useCookieConsent } from "./cookie-context";

export function CookieSettings() {
  const { preferences, showSettings, closeSettings, savePreferences } =
    useCookieConsent();

  const [localPreferences, setLocalPreferences] = useState({
    ...preferences,
  });

  const handleToggle = (cookieType: keyof typeof localPreferences) => {
    setLocalPreferences((prev) => ({
      ...prev,
      [cookieType]: !prev[cookieType],
    }));
  };

  const handleSave = () => {
    savePreferences(localPreferences);
  };

  return (
    <Dialog open={showSettings} onOpenChange={closeSettings}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Cookie Settings</DialogTitle>
          <DialogDescription>
            Customize your cookie preferences. Some cookies are necessary for
            the website to function properly.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="font-medium">Necessary Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  These cookies are essential for the website to function
                  properly.
                </p>
              </div>
              <Switch checked={true} disabled />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="font-medium">Functional Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  These cookies enable personalized features and functionality.
                </p>
              </div>
              <Switch
                checked={localPreferences.functional}
                onCheckedChange={() => handleToggle("functional")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="font-medium">Analytics Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  These cookies help us understand how visitors interact with
                  our website.
                </p>
              </div>
              <Switch
                checked={localPreferences.analytics}
                onCheckedChange={() => handleToggle("analytics")}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h4 className="font-medium">Marketing Cookies</h4>
                <p className="text-sm text-muted-foreground">
                  These cookies are used to track visitors across websites to
                  display relevant advertisements.
                </p>
              </div>
              <Switch
                checked={localPreferences.marketing}
                onCheckedChange={() => handleToggle("marketing")}
              />
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={closeSettings}>
              Cancel
            </Button>
            <Button
              className="bg-orange-600 hover:bg-orange-700"
              onClick={handleSave}
            >
              Save Preferences
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
