// Types
export interface PhoneNumberHash {
  id: string;
  hash: string;
  phone_number: string;
  new_balance?: number;
  provider: string;
  created_at?: string;
  updated_at?: string;
}

export interface Store {
  id: number;
  avatar_url: string;
  country_code: string;
  email: string;
  etims_branch: string;
  etims_cmc_key: string;
  etims_device_id: string;
  etims_device_serial: string;
  etims_environment: string;
  etims_intrl_key: string;
  etims_mrc_no: string;
  etims_sdcid: string;
  etims_sign_key: string;
  is_licensed: boolean;
  location: string;
  name: string;
  organization_id: number;
  phone_number: string;
  pin: string;
  vat: string;
  website: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  id: number;
  name: string;
  address: string;
  branch: string;
  contact_person: string;
  date_format: string;
  email: string;
  phone_number_id: number;
  phone_number: string | null;
  is_synced: boolean;
  created_at: string;
  updated_at: string;
}

export interface WholeUser {
  id: number;
  default_store: Store;
  email: string;
  first_name: string;
  last_name: string;
  organization_id: number;
  organization: Organization;
  phone_number_id: number;
  phone_number: PhoneNumber | null;
  username: string;
  is_synced: boolean;
  national_id: string;
  status: string;
  stores: Store[];
  token: string;
  created_at: string;
  updated_at: string;
}

export interface ApiKey {
  id: number;
  api_key: string;
  description: string;
  last_used_at: string | null;
  organization_id: number;
  store_id: number;
  store?: Store | null;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface JwtPayload {
  Email: string;
  ExpiresAt: number;
  Issuer: string;
  application_type: string;
  org_id: number;
  session_id: number;
  user_id: number;
}

export interface DashboardAnalytics {
  account_number: string;
  api_keys_count: number;
  user_credits: number;
  chart_summary: any[];
}

export interface TaxType {
  id: number;
  name: string;
  acronym: string;
  created_at: string;
  updated_at: string;
}

export interface TaxCategory {
  id: number;
  applicable_tax_type_id: number;
  description: string;
  has_withholding_tax: boolean;
  income_source: string;
  tax_rate: number;
  created_at: string;
  updated_at: string;
}

export interface WithholdingTaxRate {
  id: number;
  created_by: number;
  name: string;
  description: string;
  tax_category_id: number;
  withholding_tax_rate: number;
  created_at: string;
  updated_at: string;
}

export interface ExpenseType {
  id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface Attachment {
  async: boolean;
  content_md5: string;
  content_size: number;
  content_type: string;
  duration: number;
  data: string;
}

export interface Asset {
  id: number;
  url: string;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  organization_name: string;
  total_amount: number;
}

export interface HighestPayingTaxCategory {
  tax_category_name: string;
  total_amount: number;
}

export interface IncomeTotalByMonth {
  month: string;
  total: number;
  year: number;
}

export interface IncomeAnalytics {
  average_monthly_income: number;
  current_month_total_income: number;
  total_alltime_income: number;
  total_transactions_count: number;
  total_withholding_tax: number;
  total_income_tax: number;
  total_income_by_source: any[];
  total_income_by_month: any[];
  highest_paying_organization: Organization;
  highest_paying_tax_category: HighestPayingTaxCategory;
  income_totals_by_month: IncomeTotalByMonth[];
}

export interface ExpenseTotalByMonth {
  month: string;
  total: number;
  year: number;
}

export interface ExpenseAnalytics {
  average_monthly_expenses: number;
  current_month_total_expenses: number;
  total_alltime_expenses: number;
  total_expenses_by_type: any[];
  total_expenses_by_month: any[];
  total_transactions_count: number;
  top_expense_category: {
    expense_category_name: string;
    total_amount: number;
  };
  expense_totals_by_month: ExpenseTotalByMonth[];
}

export interface DashboardAnalytics {
  account_number: string;
  api_keys_count: number;
  user_credits: number;
  chart_summary: any[];
  income_analytics: IncomeAnalytics;
  expense_analytics: ExpenseAnalytics;
}

export interface ExpenseAsset {
  id: number;
  asset_id: number;
  expense_id: number;
  asset: Asset;
  created_at: string;
  updated_at: string;
}

export interface IncomeAsset {
  id: number;
  asset_id: number;
  user_income_id: number;
  asset: Asset;
  created_at: string;
  updated_at: string;
}

export interface UserExpense {
  id: number;
  expense_date: string;
  expense_type_id: number;
  expense_type: ExpenseType;
  merchant_name: string;
  merchant_pin: string;
  amount: number;
  currency: string;
  description: string;
  created_at: string;
  updated_at: string;
  attachment: Attachment;
  expense_assets: ExpenseAsset[];
}

export interface UserIncome {
  id: number;
  amount: number;
  currency: string;
  description: string;
  tax_category_id: number;
  tax_category: TaxCategory;
  income_assets: IncomeAsset[];
  income_date: string;
  merchant_name: string;
  merchant_pin: string;
  payment_frequency: string;
  start_date: string;
  end_date: string;
  withholding_tax_rate: number;
  withholding_tax_amount: number;
  created_at: string;
  updated_at: string;
}

export interface PhoneNumber {
  country_code: string;
  number: string;
}

export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: PhoneNumber;
  username: string;
  created_at: string;
  updated_at: string;
}
